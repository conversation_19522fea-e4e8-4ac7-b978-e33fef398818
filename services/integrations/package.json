{"name": "quotelier-integrations", "version": "1.0.4", "private": true, "scripts": {"deploy": "sls deploy -s $STAGE -r $REGION --domain $DOMAIN --subdomain $STAGE --verbose", "shutdown": "sls remove -s $STAGE -r $REGION --domain $DOMAIN --subdomain $STAGE --verbose", "test": "NODE_ENV=test mocha --recursive --require test/bootstrap.js -t 12000"}, "devDependencies": {"@types/chai": "^4.0.5", "@types/mocha": "^9.0.0", "@types/raven": "^2.5.1", "@types/sinon": "^10.0.2", "aws-sdk": "^2.991.0", "chai": "^4.0.2", "mocha": "^9.1.1", "nock": "^13.1.3", "sinon": "^11.1.2"}, "dependencies": {"@quotelier/contracts": "file:../../packages/contracts", "@quotelier/invokeLambda": "file:../../packages/invokeLambda", "@quotelier/logger": "file:../../packages/logger", "bluebird": "^3.5.0", "md5": "^2.2.1", "middy": "^0.36.0", "moment": "^2.19.2", "object-hash": "^2.2.0", "raven": "^2.6.4", "request": "^2.81.0", "request-promise": "^4.2.1"}}