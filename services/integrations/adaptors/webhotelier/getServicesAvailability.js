require('./../interface');
const moment = require('moment');
const { makeGET } = require('./lib/client');
const { IntegrationError } = require('../../errors');
const getPassword = require('./../../lib/getPassword');
const { getAccountRecord } = require('./../../lib/accounts');
const { reverse } = require('./lib/reverseHashedRateId');

/**
 * @override
 * @throws Error - Webhotelier's NO OK error
 * @implements {IntegrationAdaptorInterface#getServicesAvailability}
 */
function getServicesAvailability(input) {
  const accountName = input.accountName;
  const propertyCode = input.propertyCode;

  return getAccountRecord(accountName, propertyCode)
    .then(() => Promise.all([getPassword(accountName, propertyCode), reverse(input.rateId)]))
    .then(([password, rateId]) =>
      makeGET(`availability/${input.propertyCode}/extras/${rateId}`, {
        username: propertyCode,
        password: password,
        parameters: transformAvailabilityRequestToParams(input),
      })
    )
    .catch(err => {
      if (err instanceof IntegrationError && err.message === 'No extras found.') {
        // webhotelier responds with error for no availability,
        // handle and return an empty availability error.
        return { services: [] };
      } else {
        throw err;
      }
    })
    .then(response => {
      if (response && response.extras) {
        return response.extras.map(extra => transformExtrasToServices(extra));
      } else {
        return [];
      }
    });
}

/**
 * @param {GetServicesAvailabilityInput} input
 * @returns {Object}
 */
function transformAvailabilityRequestToParams(input) {
  const params = {
    checkin: moment.parseZone(input.checkin).format('YYYY-MM-DD'),
    checkout: moment
      .parseZone(input.checkin)
      .add(input.nights, 'd')
      .format('YYYY-MM-DD'),
  };

  ['adults', 'children', 'infants', 'rooms'].forEach(parameter => {
    if (input.hasOwnProperty(parameter)) {
      params[parameter] = input[parameter];
    }
  });

  if ('country' in input) {
    params.remote_country = input.country;
  }

  if ('bookingCode' in input) {
    params.bk_code = input.bookingCode;
  }

  return params;
}

/**
 * @private
 * @param {Object} extra
 * @returns {Offer}
 */
function transformExtrasToServices(extra) {
  return {
    serviceId: extra.id,
    name: extra.name,
    description: extra.description,
    price: parseInt(parseFloat(extra.price).toFixed(2) * 100),
    required: extra.required,
  };
}

module.exports = {
  getServicesAvailability,
  transformExtrasToServices,
  transformAvailabilityRequestToParams,
};
