const request = require('request');
const querystring = require('querystring');
const { NotFoundError, ForbiddenError, IntegrationError } = require('../../../errors');
const logger = require('@quotelier/logger');
const WEBHOTELIER_ENDPOINT = 'https://rest.reserve-online.net';

/**
 * @private
 * @param {string} path
 * @param {object} options
 * @param {string} options.username
 * @param {string} options.password
 * @param {object} [options.parameters]
 * @param {string} options.language
 *
 * @returns {Promise<Response>}
 */
function makeGET(path, { username, password, parameters, language }) {
  logger.log('makeGET', 'perform request', { path, username, parameters, language });

  const endpoint = process.env.WEBHOTELIER_ENDPOINT || WEBHOTELIER_ENDPOINT;
  return new Promise((resolve, reject) => {
    request(
      {
        url: `${endpoint}/${path}`,
        method: 'GET',
        headers: {
          Accept: 'application/json',
          'Accept-Language': language,
          'Accept-Encoding': 'gzip',
        },
        auth: {
          password: password,
          username: username,
        },
        qs: parameters || {},
        gzip: true,
      },
      responseHandler.bind(this, resolve, reject, path)
    );
  });
}

/**
 * @private
 * @param {string} path
 * @param {object} options
 * @param {string} options.username
 * @param {string} options.password
 * @param {object} [options.data]
 * @param {string} options.language
 *
 * @returns {Promise<Response>}
 */
function makePOST(path, { username, password, data, language }) {
  logger.log('makePOST', 'perform request', { path, username, data, language });
  const endpoint = process.env.WEBHOTELIER_ENDPOINT || WEBHOTELIER_ENDPOINT;
  return new Promise((resolve, reject) => {
    request(
      {
        url: `${endpoint}/${path}`,
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-Language': language,
          'Accept-Encoding': 'gzip',
        },
        auth: {
          password: password,
          username: username,
        },
        json: data,
        gzip: true,
      },
      responseHandler.bind(this, resolve, reject, path)
    );
  });
}

/**
 * @private
 * @param {string} path
 * @param {object} options
 * @param {string} options.username
 * @param {string} options.password
 * @param {object} [options.form]
 * @param {string} options.language
 *
 * @returns {Promise<Response>}
 */
function makeFormPOST(path, { username, password, form, language }) {
  logger.log('makePOST', 'perform request', { path, username, form, language });
  const endpoint = process.env.WEBHOTELIER_ENDPOINT || WEBHOTELIER_ENDPOINT;
  return new Promise((resolve, reject) => {
    request(
      {
        url: `${endpoint}/${path}`,
        method: 'POST',
        headers: {
          Accept: 'application/json',
          'Accept-Language': language,
          'Accept-Encoding': 'gzip',
        },
        auth: {
          password: password,
          username: username,
        },
        form: querystring.stringify(form),
        gzip: true,
      },
      responseHandler.bind(this, resolve, reject, path)
    );
  });
}

/**
 * @private
 * @param {Function} resolve
 * @param {Function} reject
 * @param {string} path
 * @param {Error} err
 * @param {object} response
 * @returns {Promise}
 */
function responseHandler(resolve, reject, path, err, response) {
  logger.log('responseHandler', 'Receive http response', response);
  if (err) {
    logger.error('responseHandler', 'Error while contacting webhotelier', err);
    err.message = `${err.message} (Method:${path})`;
    reject(err);
  } else if (response.statusCode === 403) {
    logger.error('responseHandler', 'Error while contacting webhotelier', response);
    const error = new ForbiddenError(`Access forbidden (Method:${path})`);
    error.response = response;
    reject(error);
  } else if (response.statusCode === 404) {
    logger.error('responseHandler', 'Error while contacting webhotelier', response);
    const error = new NotFoundError(`Not found (Method:${path})`);
    error.response = response;
    reject(error);
  } else {
    try {
      const { data = null, error_msg = null } = JSON.parse(response.body);

      if (data) {
        resolve(data);
      } else if (error_msg) {
        const error = new IntegrationError(error_msg);
        error.response = response;
        reject(error);
      }
    } catch (e) {
      logger.error('responseHandler', 'Error while contacting webhotelier', response);
      const error = new Error(`Unexpected status-code: ${response.statusCode} (Method:${path})`);
      error.response = response;
      reject(error);
    }
  }
}

module.exports = { makeGET, makePOST, makeFormPOST };
