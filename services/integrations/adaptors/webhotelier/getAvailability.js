const md5 = require('md5');
const moment = require("moment");

require('./../interface');
const { makeGET } = require('./lib/client');
const { IntegrationError } = require('../../errors');
const getPassword = require('./../../lib/getPassword');
const { getAccountRecord } = require('./../../lib/accounts');
const parameters = require('./../../lib/parameters');


/**
 * @override
 * @throws Error - Webhotelier's NO OK error
 * @implements {IntegrationAdaptorInterface#getAvailability}
 */
function getAvailability(input) {
  const accountName = input.accountName;
  const propertyCode = input.propertyCode;
  const breakdownFlag = input.breakdown;

  return Promise.all([
    getPassword(accountName, propertyCode),
    getAccountRecord(accountName, propertyCode),
  ])
    .then(results =>
      makeGET(`availability/${input.propertyCode}`, {
        username: propertyCode,
        password: results[0],
        parameters: Object.assign(
          { offline: results[1].wh_offline === true ? 1 : 0 },
          transformAvailabilityRequestToParams(input)
        ),
      })
    )
    .catch(err => {
      if (
        err instanceof IntegrationError &&
        err.message === 'No available rates have been found.'
      ) {
        // webhotelier responds with error for no availability,
        // handle and return an empty availability error.
        return { rates: [] };
      } else {
        throw err;
      }
    })
    .then(response => {
      if (response && response.rates) {
        return response.rates.map(
          rate =>
            breakdownFlag
              ? transformBreakdownRateToOffer(rate, response.currency)
              : transformRateToOffer(rate, input.nights, response.currency)
        );
      } else {
        return [];
      }
    })
    .then(availabilityOffers => assignQuotelierRateReference(availabilityOffers));
}

/**
 * Generates the quotelier rate-id and stores the dynamic webhotelier rate
 * references as adaptor-parameters with the md5-hash of property, rate, policies.
 *
 * This is necessary because the availability responds with dynamic rate-references (policy-ids etc.)
 * we have to store the combination of rate and policies and persist the references.
 *
 * Usecase: Once you try to access the rate with the quotelier rate-id, the adaptor will be able
 * to access the references of the rate (parameters).
 *
 * @param {QOffer[]} offers
 * @returns {Promise<QOffer[]>}
 */
function assignQuotelierRateReference(offers) {
  const batchPuts = offers.map(offer => {
    const quotelierRateId = md5(
      `${offer.propertyCode}:${offer.rateId}:${offer.paymentPolicyId}:${offer.cancellationPolicyId}`
    );
    const webhotelierRateId = offer.rateId;
    offer.rateId = quotelierRateId;
    return {
      adaptor: 'webhotelier',
      id: quotelierRateId,
      params: {
        rateId: webhotelierRateId,
        cancellationPolicyId: offer.cancellationPolicyId,
        paymentPolicyId: offer.paymentPolicyId,
      },
    };
  });

  // store new abstract quotelier rate references as adaptor parameter
  return parameters.setBatchParameters(batchPuts).then(() => offers);
}

/**
 * @param {GetAvailabilityInput} input
 * @returns {Object}
 */
function transformAvailabilityRequestToParams(input) {

  const params = {
    checkin: moment.parseZone(input.checkin).format("YYYY-MM-DD"),
    nights: input.nights,
  };

  ['adults', 'children', 'infants', 'rooms', 'board'].forEach(parameter => {
    if (input.hasOwnProperty(parameter)) {
      params[parameter] = input[parameter];
    }
  });

  if ('propertyCode' in input) {
    params.property = input.propertyCode;
  }

  if ('breakdown' in input && input.breakdown) {
    params.breakdown = 1;
  }

  if ('accommodationCode' in input) {
    params.room = input.accommodationCode;
  }

  if ('country' in input) {
    params.remote_country = input.country;
  }

  if ('bookingCode' in input) {
    params.bk_code = input.bookingCode;
  }

  return params;
}

/**
 * @private
 * @param {Object} rate
 * @param {String} nights
 * @param {String} currency
 * @returns {Offer}
 */
function transformRateToOffer(rate, nights, currency) {
  // avoid negative discount
  rate.pricing.discount = rate.pricing.discount < 0 ? 0 : rate.pricing.discount;
  const roomRate = (rate.pricing.price / nights).toFixed(2);
  const officialRate = ((rate.pricing.price + rate.pricing.discount) / nights).toFixed(2);
  const discountRate = (rate.pricing.discount / nights).toFixed(2);
  const taxesRate = (rate.pricing.taxes / nights).toFixed(2);
  const excludedCharges = (rate.pricing.excluded_charges / nights).toFixed(2);

  return {
    rateId: rate.id,
    boardId: rate.board,
    rate: rate.rate,
    accommodationCode: rate.type,
    serviceCode: rate.serviceCodes || null,
    room: rate.room,
    currency,
    paymentPolicyId: rate.payment_policy_id || null,
    cancellationPolicyId: rate.cancellation_policy_id || null,
    cancellationExpiration: rate.cancellation_expiry || null,
    remaining: rate.remaining,
    roomRate: parseInt(roomRate * 100),
    officialRate: parseInt(officialRate * 100),
    discountRate: parseInt(discountRate * 100),
    taxesRate: parseInt(taxesRate * 100),
    excludedCharges: parseInt(excludedCharges * 100),
  };
}

/**
 * @private
 * @param {Object} rate
 * @param {String} currency
 * @returns {Offer}
 */
function transformBreakdownRateToOffer(rate, currency) {
  const statusExists = rate.status !== undefined;
  const statusExistsAndIsNotAvailable = statusExists && rate.status !== 'AVL';
  const rateStatus = statusExistsAndIsNotAvailable ? 'NOT_AVAILABLE' : 'AVAILABLE';

  return {
    rateId: rate.id,
    boardId: rate.board,
    rate: rate.rate,
    accommodationCode: rate.type,
    serviceCode: rate.serviceCodes || null,
    room: rate.room,
    currency,
    paymentPolicyId: rate.payment_policy_id || null,
    cancellationPolicyId: rate.cancellation_policy_id || null,
    status: rateStatus,
    statusMessage: statusExistsAndIsNotAvailable ? rate.status_descr : null,
    days: rate.days.map(day => ({
      date: day.date,
      remaining: day.remaining,
      status: rateStatus,
      roomRate: parseInt(day.price.toFixed(2) * 100),
      officialRate: parseInt(day.initial.toFixed(2) * 100),
      discountRate: parseInt(day.discount.toFixed(2) * 100),
    })),
  };
}

module.exports = {
  getAvailability,
  transformRateToOffer,
  transformAvailabilityRequestToParams,
};
