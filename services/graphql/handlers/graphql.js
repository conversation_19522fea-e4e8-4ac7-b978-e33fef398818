'use strict';

const { graphql } = require('graphql');
const { ACL } = require('../libs/acl/permission');
const { verifyJWT } = require('../libs/acl/verifyJWT');
const accessControlDecorator = require('./../libs/acl/access');
const { schema } = require('./../libs/Schema');
const createHttpResponse = require('./../libs/utils/createHttpResponse');
const logger = require('@quotelier/logger');
const middlewares = require('../middlewares');

accessControlDecorator.decorate(schema);

/**
 *
 * This is the internal GraphQL Handler that also being used by the frontend
 * application.
 *
 * @param {Object} event
 * @param {Object} context
 * @param {Function} cb
 * @returns {void}
 */
function graphqlHandler(event, context, cb) {
  logger.log('graphqlHandler', 'GraphQL Request', event);

  let body;
  let variables;

  try {
    body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    variables =
      body && body.variables && typeof body.variables === 'string'
        ? JSON.parse(body.variables)
        : body.variables;
  } catch (e) {
    const message = 'Cannot parse body.';
    return cb(null, createHttpResponse({ message }, 400));
  }
  let graphqlContext;
  try {
    graphqlContext = createContext(event);
  } catch (e) {
    const message = 'Authorization header is missing or is not well formed.';
    return cb(null, createHttpResponse({ message }, 401));
  }

  graphql(
    schema,
    body ? body.query : null,
    null,
    graphqlContext,
    variables,
    body ? body.operationName : null
  )
    .then(response => cb(null, createHttpResponse(response, 200)))
    .catch(err => cb(null, createHttpResponse(err, 500)));
}

/**
 * @param {Object} event
 * @returns {Object}
 */
function createContext(event) {
  const jwtData = verifyJWT(event.headers['Authorization']);
  return {
    jwtData,
    authorizationHeader: event.headers ? event.headers['Authorization'] : null,
    acl: new ACL(jwtData),
  };
}


module.exports = {
  handler: middlewares.init(graphqlHandler)
}
