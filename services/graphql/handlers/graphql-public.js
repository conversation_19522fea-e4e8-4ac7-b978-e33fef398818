'use strict';

const graphql = require('graphql').graphql;
const { maskErrors } = require('graphql-errors');

const Schema = require('./../libs/Schema').publicSchema;
const createHttpResponse = require('./../libs/utils/createHttpResponse');
const logger = require('@quotelier/logger');
const middlewares = require('../middlewares');

/**
 * <PERSON><PERSON> requests to GraphQL from API Gateway
 *
 * @param {Object} event
 * @param {string} event.body
 * @param {Object} event.headers
 * @param {Object} event.query
 * @param {Object} context
 * @param {Function} cb
 * @returns {void}
 */
function graphqlPublicHandler(event, context, cb) {
  logger.log('graphqlHandler', 'GraphQL Request', event);

  let body;
  let variables;

  try {
    body = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    variables =
      body && body.variables && typeof body.variables === 'string'
        ? JSON.parse(body.variables)
        : body.variables;
  } catch (e) {
    const message = 'Cannot parse body.';
    return cb(null, createHttpResponse({ message }, 400));
  }

  let authorizationHeader;
  try {
    authorizationHeader = event.headers['Authorization'].split('Bearer ')[1];
  } catch (e) {
    const message = 'Authorization header is missing or is not well formed.';
    return cb(null, createHttpResponse({ message }, 401));
  }

  maskErrors(Schema);
  return graphql(
    Schema,
    body ? body.query : null,
    null,
    { authorizationHeader },
    variables,
    body ? body.operationName : null
  )
    .then(response => {
      if (response.errors && response.errors.length) {
        response = response.errors[0].message
      }

      return cb(null, createHttpResponse(response, 200))
    })
    .catch(err => cb(null, createHttpResponse(err, 500)));
}

module.exports = {
  handler: middlewares.init(graphqlPublicHandler)
};
