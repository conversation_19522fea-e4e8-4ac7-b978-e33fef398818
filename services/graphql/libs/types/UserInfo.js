const {
  GraphQLObjectType,
  GraphQLString,
  GraphQLBoolean,
  GraphQLList,
  GraphQLInt,
} = require('graphql');

const UserInfo = new GraphQLObjectType({
  name: 'UserInfo',
  description: 'Information for the currently logged in user',
  fields: () => ({
    operatorId: {
      type: GraphQLString,
    },
    intercomHash: {
      type: GraphQLString,
    },
    accountName: {
      type: GraphQLString,
    },
    preferences: {
      type: new GraphQLObjectType({
        name: 'OperatorPreferences',
        fields: {
          previewIsRequired: {
            name: 'previewIsRequired',
            type: GraphQLBoolean,
          },
          discountWarning: {
            name: 'discountWarning',
            type: GraphQLInt,
          },
          customServices: {
            name: 'customServices',
            type: GraphQLBoolean,
          },
          overwriteRates: {
            name: 'overwriteRates',
            type: GraphQLBoolean,
          },
          overwriteOfficialRate: {
            name: 'overwriteOfficialRate',
            type: GraphQLBoolean,
          },
          operatorCodeName: {
            name: 'operatorCodeName',
            type: GraphQLString,
          },
          requiredFields: {
            name: 'requiredFields',
            type: new GraphQLList(GraphQLString),
          },
        },
      }),
    },
  }),
});

module.exports = UserInfo;
