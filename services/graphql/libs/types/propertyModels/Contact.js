const GraphQL = require('graphql');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;

const Contact = new GraphQLObjectType({
  name: 'Contact',
  fields: () => ({
    tel: {
      type: GraphQLString,
    },
    fax: {
      type: GraphQLString,
    },
    email: {
      type: GraphQLString,
    },
    skype: {
      type: GraphQLString,
    },
  }),
});

module.exports = Contact;
