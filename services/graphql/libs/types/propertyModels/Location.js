const GraphQL = require('graphql');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;
const GraphQLFloat = GraphQL.GraphQLFloat;

const Location = new GraphQLObjectType({
  name: 'Location',
  fields: () => ({
    lat: {
      type: GraphQLFloat,
    },
    lon: {
      type: GraphQLFloat,
    },
    utc_offset: {
      type: GraphQLFloat,
    },
    timezone: {
      type: GraphQLString,
    },
    name: {
      type: GraphQLString,
    },
    address: {
      type: GraphQLString,
    },
    zip: {
      type: GraphQLString,
    },
    country: {
      type: GraphQLString,
    },
  }),
});

module.exports = Location;
