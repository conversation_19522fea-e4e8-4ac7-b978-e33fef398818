const GraphQL = require('graphql');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;

const Operation = new GraphQLObjectType({
  name: 'Operation',
  fields: () => ({
    checkout_time: {
      type: GraphQLString,
    },
    checkin_time: {
      type: GraphQLString,
    },
    open_from: {
      type: GraphQLString,
    },
    open_to: {
      type: GraphQLString,
    },
  }),
});

module.exports = Operation;
