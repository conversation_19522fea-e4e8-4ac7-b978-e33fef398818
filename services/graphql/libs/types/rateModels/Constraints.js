const GraphQL = require('graphql');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;
const GraphQLInt = GraphQL.GraphQLInt;

const Constraints = new GraphQLObjectType({
  name: 'Constraints',
  description: 'Quotelier Constraints Class',
  fields: () => ({
    expiration: {
      type: GraphQLString,
    },
    earlyBookLimit: {
      type: GraphQLInt,
    },
    freeCancelDays: {
      type: GraphQLInt,
    },
  }),
});

module.exports = Constraints;
