const GraphQL = require('graphql');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;
const GraphQLCustomTypes = require('../Types');
const PropertyType = require('../Property');
const GraphQLList = GraphQL.GraphQLList;
const GraphQLNonNull = GraphQL.GraphQLNonNull;

const UpsaleDetails = new GraphQLObjectType({
  name: 'UpsaleDetails',
  fields: () => ({
    propertyCode: {
      type: new GraphQLNonNull(GraphQLString),
    },
    accountName: {
      type: new GraphQLNonNull(GraphQLString),
    },
    property: {
      type: new GraphQLNonNull(PropertyType),
    },
    location: {
      type: GraphQLString,
    },
    tags: {
      type: new GraphQLList(GraphQLString),
    },
    notes: {
      type: GraphQLString,
    },
    message: {
      type: GraphQLString,
    },
  }),
});

const UpsaleDetailsInput = new GraphQL.GraphQLInputObjectType({
  name: 'UpsaleDetailsInput',
  fields: () => ({
    propertyCode: {
      type: new GraphQL.GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    tags: {
      type: new GraphQLList(GraphQLCustomTypes.GraphQLStringInput),
    },
    notes: {
      type: GraphQLString,
    },
    message: {
      type: GraphQLString,
    },
  }),
});

module.exports = {
  UpsaleDetails,
  UpsaleDetailsInput,
};
