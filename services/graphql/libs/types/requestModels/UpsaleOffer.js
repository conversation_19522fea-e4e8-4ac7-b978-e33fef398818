const GraphQL = require('graphql');
const GraphQLCustomTypes = require('../Types');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;
const GraphQLInt = GraphQL.GraphQLInt;
const GraphQLList = GraphQL.GraphQLList;
const Service = require('../Service');
const Room = require('../Room');
const Rate = require('../Rate');

const Offer = new GraphQLObjectType({
  name: 'UpsaleOffer',
  fields: () => ({
    rateId: {
      // webhotelier attribute
      type: GraphQLString,
    },
    rate: {
      type: Rate,
    },
    propertyCode: {
      type: GraphQLString,
    },
    reservationId: {
      type: GraphQLInt,
    },
    accommodation: {
      type: new GraphQLList(Room),
    },
    services: {
      type: new GraphQLList(Service),
    },
    accommodationCodes: {
      type: new GraphQLList(GraphQLString),
    },
    serviceCodes: {
      type: new GraphQLList(GraphQLString),
    },
    title: {
      type: GraphQLString,
    },
    description: {
      type: GraphQLString,
    },
    checkin: {
      type: GraphQLString,
    },
    paymentUrl: {
      type: GraphQLString,
    },
    nights: {
      type: GraphQL.GraphQLInt,
    },
    rooms: {
      type: GraphQL.GraphQLInt,
    },
    children: {
      type: GraphQL.GraphQLInt,
    },
    adults: {
      type: GraphQL.GraphQLInt,
    },
    infants: {
      type: GraphQL.GraphQLInt,
    },
    officialRate: {
      type: GraphQL.GraphQLFloat,
    },
    roomRate: {
      type: GraphQL.GraphQLFloat,
    },
    discountRate: {
      type: GraphQL.GraphQLFloat,
    },
    taxesRate: {
      type: GraphQL.GraphQLFloat,
    },
    cancellationExpiration: {
      type: GraphQLString,
    },
    excludedCharges: {
      type: GraphQL.GraphQLFloat,
    },
    currency: {
      type: GraphQLString,
    },
    accepted: {
      type: GraphQL.GraphQLBoolean,
    },
  }),
});

const PrimitiveOffer = new GraphQLObjectType({
  name: 'PrimitiveUpsaleOffer',
  fields: () => ({
    rateId: {
      type: GraphQLString,
    },
    propertyCode: {
      type: GraphQLString,
    },
    reservationId: {
      type: GraphQLInt,
    },
    accommodationCodes: {
      type: new GraphQLList(GraphQLString),
    },
    serviceCodes: {
      type: new GraphQLList(GraphQLString),
    },
    title: {
      type: GraphQLString,
    },
    description: {
      type: GraphQLString,
    },
    checkin: {
      type: GraphQLString,
    },
    nights: {
      type: GraphQL.GraphQLInt,
    },
    rooms: {
      type: GraphQL.GraphQLInt,
    },
    children: {
      type: GraphQL.GraphQLInt,
    },
    adults: {
      type: GraphQL.GraphQLInt,
    },
    infants: {
      type: GraphQL.GraphQLInt,
    },
    officialRate: {
      type: GraphQL.GraphQLFloat,
    },
    roomRate: {
      type: GraphQL.GraphQLFloat,
    },
    discountRate: {
      type: GraphQL.GraphQLFloat,
    },
    taxesRate: {
      type: GraphQL.GraphQLFloat,
    },
    cancellationExpiration: {
      type: GraphQLString,
    },
    excludedCharges: {
      type: GraphQL.GraphQLFloat,
    },
    currency: {
      type: GraphQLString,
    },
    accepted: {
      type: GraphQL.GraphQLBoolean,
    },
  }),
});

const OfferInput = new GraphQL.GraphQLInputObjectType({
  name: 'UpsaleOfferInput',
  fields: () => ({
    rateId: {
      type: new GraphQL.GraphQLNonNull(GraphQLString),
    },
    propertyCode: {
      type: new GraphQL.GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    accommodationCodes: {
      type: new GraphQL.GraphQLNonNull(new GraphQLList(GraphQLCustomTypes.GraphQLStringInput)),
    },
    serviceCodes: {
      type: new GraphQLList(GraphQLCustomTypes.GraphQLStringInput),
    },
    title: {
      type: new GraphQL.GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    description: {
      type: new GraphQL.GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    checkin: {
      type: new GraphQL.GraphQLNonNull(GraphQLCustomTypes.DateString),
    },
    nights: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLInt),
    },
    rooms: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLInt),
    },
    children: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLInt),
    },
    adults: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLInt),
    },
    infants: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLInt),
    },
    officialRate: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLFloat),
    },
    roomRate: {
      type: new GraphQL.GraphQLNonNull(GraphQL.GraphQLFloat),
    },
    discountRate: {
      type: GraphQL.GraphQLFloat,
    },
    taxesRate: {
      type: GraphQL.GraphQLFloat,
    },
    cancellationExpiration: {
      type: GraphQLString,
    },
    excludedCharges: {
      type: GraphQL.GraphQLFloat,
    },
    currency: {
      type: new GraphQL.GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
  }),
});

module.exports = {
  Offer,
  PrimitiveOffer,
  OfferInput,
};
