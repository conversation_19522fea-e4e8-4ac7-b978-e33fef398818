const {
  GraphQLObjectType,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat,
  GraphQLList,
  GraphQLBoolean,
  GraphQLInputObjectType,
  GraphQLNonNull,
} = require('graphql');

const GraphQLCustomTypes = require('../Types');

const Service = require('../Service');
const Room = require('../Room');
const Rate = require('../Rate');
const Property = require('../Property');

const Offer = new GraphQLObjectType({
  name: 'UpgradeOffer',
  fields: () => ({
    rateId: {
      // webhotelier attribute
      type: GraphQLString,
    },
    rate: {
      type: Rate,
    },
    propertyCode: {
      type: GraphQLString,
    },
    property: { type: Property },
    reservationId: {
      type: GraphQLInt,
    },
    accommodation: {
      type: new GraphQLList(Room),
    },
    services: {
      type: new GraphQLList(Service),
    },
    accommodationCodes: {
      type: new GraphQLList(GraphQLString),
    },
    serviceCodes: {
      type: new GraphQLList(GraphQLString),
    },
    title: {
      type: GraphQLString,
    },
    description: {
      type: GraphQLString,
    },
    checkin: {
      type: GraphQLString,
    },
    paymentUrl: {
      type: GraphQLString,
    },
    nights: {
      type: GraphQLInt,
    },
    rooms: {
      type: GraphQLInt,
    },
    children: {
      type: GraphQLInt,
    },
    adults: {
      type: GraphQLInt,
    },
    infants: {
      type: GraphQLInt,
    },
    officialRate: {
      type: GraphQLFloat,
    },
    roomRate: {
      type: GraphQLFloat,
    },
    discountRate: {
      type: GraphQLFloat,
    },
    taxesRate: {
      type: GraphQLFloat,
    },
    cancellationExpiration: {
      type: GraphQLString,
    },
    excludedCharges: {
      type: GraphQLFloat,
    },
    currency: {
      type: GraphQLString,
    },
    accepted: {
      type: GraphQLBoolean,
    },
  }),
});

const PrimitiveOffer = new GraphQLObjectType({
  name: 'PrimitiveUpgradeOffer',
  fields: () => ({
    rateId: {
      type: GraphQLString,
    },
    propertyCode: {
      type: GraphQLString,
    },
    reservationId: {
      type: GraphQLInt,
    },
    accommodationCodes: {
      type: new GraphQLList(GraphQLString),
    },
    serviceCodes: {
      type: new GraphQLList(GraphQLString),
    },
    title: {
      type: GraphQLString,
    },
    description: {
      type: GraphQLString,
    },
    checkin: {
      type: GraphQLString,
    },
    nights: {
      type: GraphQLInt,
    },
    rooms: {
      type: GraphQLInt,
    },
    children: {
      type: GraphQLInt,
    },
    adults: {
      type: GraphQLInt,
    },
    infants: {
      type: GraphQLInt,
    },
    officialRate: {
      type: GraphQLFloat,
    },
    roomRate: {
      type: GraphQLFloat,
    },
    discountRate: {
      type: GraphQLFloat,
    },
    taxesRate: {
      type: GraphQLFloat,
    },
    cancellationExpiration: {
      type: GraphQLString,
    },
    excludedCharges: {
      type: GraphQLFloat,
    },
    currency: {
      type: GraphQLString,
    },
    accepted: {
      type: GraphQLBoolean,
    },
  }),
});

const OfferInput = new GraphQLInputObjectType({
  name: 'UpgradeOfferInput',
  fields: () => ({
    rateId: {
      type: new GraphQLNonNull(GraphQLString),
    },
    propertyCode: {
      type: new GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    accommodationCodes: {
      type: new GraphQLNonNull(new GraphQLList(GraphQLCustomTypes.GraphQLStringInput)),
    },
    serviceCodes: {
      type: new GraphQLList(GraphQLCustomTypes.GraphQLStringInput),
    },
    title: {
      type: new GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    description: {
      type: new GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
    checkin: {
      type: new GraphQLNonNull(GraphQLCustomTypes.DateString),
    },
    nights: {
      type: new GraphQLNonNull(GraphQLInt),
    },
    rooms: {
      type: new GraphQLNonNull(GraphQLInt),
    },
    children: {
      type: new GraphQLNonNull(GraphQLInt),
    },
    adults: {
      type: new GraphQLNonNull(GraphQLInt),
    },
    infants: {
      type: new GraphQLNonNull(GraphQLInt),
    },
    officialRate: {
      type: new GraphQLNonNull(GraphQLFloat),
    },
    roomRate: {
      type: new GraphQLNonNull(GraphQLFloat),
    },
    discountRate: {
      type: GraphQLFloat,
    },
    taxesRate: {
      type: GraphQLFloat,
    },
    cancellationExpiration: {
      type: GraphQLString,
    },
    excludedCharges: {
      type: GraphQLFloat,
    },
    currency: {
      type: new GraphQLNonNull(GraphQLCustomTypes.GraphQLStringInput),
    },
  }),
});

module.exports = {
  Offer,
  PrimitiveOffer,
  OfferInput,
};
