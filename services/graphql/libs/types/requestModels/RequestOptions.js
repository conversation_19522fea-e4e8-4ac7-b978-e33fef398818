const GraphQL = require('graphql');
const GraphQLObjectType = GraphQL.GraphQLObjectType;
const GraphQLString = GraphQL.GraphQLString;

const RequestOptions = new GraphQLObjectType({
  name: 'RequestOptions',
  fields: () => ({
    flowType: {
      type: GraphQLString,
    },
  }),
});

const RequestOptionsInput = new GraphQL.GraphQLInputObjectType({
  name: 'RequestOptionsInput',
  fields: () => ({
    flowType: {
      type: GraphQLString,
    },
  }),
});

module.exports = {
  RequestOptions,
  RequestOptionsInput,
};
