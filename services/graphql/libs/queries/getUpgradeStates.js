const { invokeService } = require('./../utils/invokeLambdaNative');

/**
 * @param {{upgradeId: string}} args
 * @param {ACL} acl
 *
 * @returns {*}
 */
function getUpgradeStates(args, { acl }) {
  const { accountName } = acl.access;
  const { propertyCode } = args;

  return invokeService('workflows', 'getTransitionStates', {
    accountName,
    propertyCode,
    language: 'en',
    component: 'upgrade',
  });
}

module.exports = { getUpgradeStates };
