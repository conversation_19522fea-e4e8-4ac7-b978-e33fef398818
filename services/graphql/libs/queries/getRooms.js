const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @returns {Promise<QRate>}
 */
function getRooms({ accountName, language, propertyCode }) {
  return invokeService('integrations', 'getRooms', {
    accountName,
    language,
    propertyCode,
  });
}

module.exports = { getRooms };
