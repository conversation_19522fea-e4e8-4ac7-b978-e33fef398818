const { invokeService } = require('./../utils/invokeLambdaNative');

/**
 * @param {{upgrade: QPrimitiveUpgrade}} args
 * @param {{acl: ACL}} context
 * @returns {Promise<QRate>}
 */
function getUpgradePreview(args, context) {
  const { accountName, operatorId } = context.acl.access;
  Object.assign(args.upgrade.details, { accountName, operatorId });

  return invokeService('integrations', 'getReservation', {
    accountName,
    propertyCode: args.upgrade.details.propertyCode,
    reservationId: args.upgrade.reservationId,
  })
    .then(({ response }) => {
      Object.assign(args.upgrade, {
        reservation: response,
        activities: {
          created: '' + Date.now(),
          updated: '' + Date.now(),
        },
        id: 'example-xy-upgrade',
        state: 'sent',
      });

      args.upgrade.details = Object.assign(
        {
          template: 'proposal',
          language: 'en',
          muted: false,
        },
        args.upgrade.details
      );

      return invokeService('quotelier-upgrade', 'populateUpgrade', args.upgrade);
    })
    .then(upgrade => {
      return invokeService('quotelier-upgrade', 'renderProposal', {
        upgrade,
        previewMode: true,
      }).then(({ content }) => content);
    });
}

module.exports = { getUpgradePreview };
