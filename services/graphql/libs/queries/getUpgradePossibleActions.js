const { invokeService } = require('./../utils/invokeLambdaNative');

/**
 * @param {{upgradeId: string}} args
 * @returns {*}
 */
function getUpgradePossibleActions(args) {
  return invokeService('quotelier-upgrade', 'getUpgrade', args).then(upgrade => {
    return invokeService('workflows', 'nextTransitions', {
      accountName: upgrade.details.accountName,
      language: upgrade.details.language,
      propertyCode: upgrade.details.propertyCode,
      app: 'upgrade',
      currentState: upgrade.state,
    });
  });
}

module.exports = { getUpgradePossibleActions };
