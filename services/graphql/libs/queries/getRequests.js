const { invokeService } = require('./../utils/invokeLambda');
const deprecationSupport = require('../utils/requestDeprecationSupport');

/**
 * @param {object} args
 * @param {boolean} args.onlyOwned
 * @param {string} args.term
 * @param {string} args.propertyCode
 * @param {string} args.state
 * @param {string} args.markedToFollowUp
 * @param {object} context
 * @param {ACL} context.acl
 *
 * @returns {Promise<any>}
 */
function getRequests(args, context) {
  return invokeService('indexing', 'search', {
    indexName: 'newrequests',
    body: buildElasticSearchDSL(Object.assign({ acl: context.acl }, args)),
  }).then(response => response.hits.map(request => deprecationSupport(request)));
}

/**
 * @param {boolean} onlyOwned
 * @param {string} term
 * @param {string} propertyCode
 * @param {string} state
 * @param {string} markedToFollowUp
 * @param {ACL} acl
 *
 * @returns {object}
 */
function buildElasticSearchDSL({ onlyOwned, term, propertyCode, state, markedToFollowUp, acl }) {
  const accountName = acl.access.accountName;
  const operatorId = acl.access.operatorId;

  let body = {
    from: 0,
    size: 30,
    sort: [],

    /**
     * Exclude all nested properties with the name "photos".
     * @todo: Remove this exclusion once all request with the heavy property-object are expired.
     * @see https://headinbeds.atlassian.net/browse/QSLS-772
     */
    _source: {
      exclude: ['*.photos'],
    },
    query: {
      bool: {
        must: [{ match: { 'request.accountName.keyword': accountName } }],
        must_not: [],
      },
    },
  };
  /**
   * PROPERTY PERMISSION SETUP
   */
  body.query.bool.must.push({
    bool: {
      should: getAllowedProperties(acl.access.permissions)
        .map(propertyCode => {
          return { match: { 'request.propertyCode.keyword': propertyCode } };
        })
        .concat([{ match: { 'operatorId.keyword': operatorId } }]),
      minimum_should_match: 1,
    },
  });

  /**
   * CUSTOM FILTERING
   */
  if (term && term !== '') {
    body.sort.push('_score');
    body.query.bool.should = [
      {
        multi_match: {
          fields: ['contact.name', 'contact.nickname', 'contact.email', 'contact.phone'],
          query: term,
          type: 'phrase_prefix',
        },
      },
      { match: { id: term } },
    ];
    body.query.bool.minimum_should_match = 1;
  } else {
    body.sort.push({ 'activities.updated': { order: 'desc' } });
    body.sort.push({ updatedAt: { order: 'desc' } });
  }

  if (onlyOwned) {
    body.query.bool.must.push({ match: { 'operatorId.keyword': operatorId } });
  }

  const markedToFollowUpQuery = {
    bool: {
      should: [
        { exists: { field: 'activties.markedToFollowUp' } },
        { exists: { field: 'markedToFollowUp' } },
      ],
      minimum_should_match: 1,
    },
  };

  if (markedToFollowUp === true) {
    body.query.bool.must.push(markedToFollowUpQuery);
  } else if (markedToFollowUp === false) {
    body.query.bool.must_not.push(markedToFollowUpQuery);
  }

  if (state) {
    if (state === 'archived') {
      body.query.bool.must.push({ match: { 'state.keyword': 'archived' } });
    } else if (state !== 'all') {
      body.query.bool.must.push({ match: { 'state.keyword': state } });
      body.query.bool.must_not.push({ match: { 'contact.wiped': true } });
    } else if (state === 'all') {
      body.query.bool.must_not.push({ match: { 'state.keyword': 'archived' } });
      body.query.bool.must_not.push({ match: { 'state.keyword': 'expired' } });
      body.query.bool.must_not.push({ match: { 'contact.wiped': true } });
    }
  }
  if (propertyCode) {
    body.query.bool.must.push({ match: { 'request.propertyCode.keyword': propertyCode } });
  }
  return body;
}

/**
 * @param {Array<object<string, string>>} permissions
 * @returns {Array<string>}
 */
function getAllowedProperties(permissions) {
  return permissions
    .filter(permission => {
      return permission[Object.keys(permission)[0]] === 'index';
    })
    .map(permission => Object.keys(permission)[0]);
}

module.exports = { getRequests, buildElasticSearchDSL };
