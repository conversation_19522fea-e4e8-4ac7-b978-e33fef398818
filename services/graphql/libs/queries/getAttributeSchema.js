const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {string} accountName
 * @param {string} language
 * @param {string} propertyCode
 * @returns {Promise<any>}
 */
function getAttributeSchema({ accountName, language, propertyCode }) {
  return invokeService('properties-service', 'getSetting', {
    accountName,
    language,
    propertyCode,
    settingCode: 'requestAttributes',
  });
}

module.exports = { getAttributeSchema };
