const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {string} accountName
 * @param {string} propertyCode
 * @param {string} from
 * @param {string} to
 *
 * @returns {Promise<Array<QOpportunity>>}
 */
function getOpportunities({ accountName, propertyCode, from, to }) {
  return invokeService('upsales', 'getOpportunities', {
    accountName,
    propertyCode,
    from,
    to,
  });
}

module.exports = { getOpportunities };
