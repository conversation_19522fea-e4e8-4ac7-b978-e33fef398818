const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.roomCode
 * @param {string} arguments.rateId
 * @param {string} arguments.serviceId
 * @returns {Promise<QRate>}
 */
function getService({ accountName, language, propertyCode, roomCode, rateId, serviceId }) {
  return invokeService('integrations', 'getService', {
    accountName,
    language,
    propertyCode,
    roomCode,
    rateId,
    serviceId,
  });
}

module.exports = { getService };
