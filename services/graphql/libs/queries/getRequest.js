const { invokeService } = require('./../utils/invokeLambda');
const queryHasSelection = require('./../utils/queryHasSelection');
const deprecationSupport = require('../utils/requestDeprecationSupport');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.requestId
 * @param {string} arguments.withPopulations
 * @returns {Promise<QRequest>}
 */
function getRequest({ requestId, withPopulations }) {
  const params = {
    id: requestId,
  };

  if (withPopulations) {
    params.with = withPopulations;
  }
  return invokeService('new-request', 'getRequest', params).then(response =>
    Object.assign({}, deprecationSupport(response), {
      emailTemplate: response.template,
    })
  );
}

/**
 * @param {string} requestId
 * @param {GqlAST} AST
 * @returns {Promise<QRequest>}
 */
function getRequestFromAST({ requestId, AST }) {
  const withPopulations = [];
  if (queryHasSelection(AST, `${AST.fieldName}.operator`)) {
    withPopulations.push('operator');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.request.property`)) {
    withPopulations.push('property');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.attributes`)) {
    withPopulations.push('attributes');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.rate`)) {
    withPopulations.push('rates');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.accommodation`)) {
    withPopulations.push('accommodations');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.rate.board`)) {
    withPopulations.push('boards');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.services`)) {
    withPopulations.push('services');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.reservation`)) {
    withPopulations.push('reservations');
  }

  return getRequest({ requestId, withPopulations });
}

module.exports = { getRequest, getRequestFromAST };
