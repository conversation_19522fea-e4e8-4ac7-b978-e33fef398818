const { invokeService } = require('./../utils/invokeLambda');

const normalizePayload = require('./../utils/normalizePayload');

/**
 *
 * @param {Object} arguments
 * @param {QPrimitiveUpsale} arguments.upsale
 * @returns {Promise<QRate>}
 */
function getUpsalePreview({ upsale }) {
  return invokeService('upsales', 'renderUpsale', normalizePayload(upsale), {
    previewMode: true,
  });
}

module.exports = { getUpsalePreview };
