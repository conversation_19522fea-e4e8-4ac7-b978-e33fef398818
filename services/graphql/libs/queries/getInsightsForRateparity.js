// Get property stats for RateParity
// Query AWS Athena rateparity database to get revenue, total reservations and total requests for the property
//

const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {object} args
 * @param {string} args.propertyCode
 * @param {string} args.from
 * @param {string} args.to
 * @param {object} context
 * @param {ACL} context.acl
 *
 * @returns {Promise<any>}
 */

function getInsightsForRateparity(args, context) {
  return invokeService('insights-service', 'athenaQuery', {
    query: buildAthenaQuery(Object.assign(args)),
  }).then((response) => fillInBlankDates(args.from, args.to, response));
}

/**
 * @param {string} from
 * @param {string} to
 * @param {object} queryResults
 * @returns {object}
 */

function fillInBlankDates(from, to, queryResults) {
  let zeroValueObjects = generateZeroValueObjects(from, to);

  // Merge the zero value objects with the query results
  // and null safe the query results

  let mergedResults;

  if (queryResults && queryResults.length > 0) {
    mergedResults = zeroValueObjects.map((zeroValueObject) => {
      let queryResult = queryResults.find((queryResult) => {
        return queryResult.day === zeroValueObject.day;
      });

      if (queryResult) {
        return queryResult;
      } else {
        return zeroValueObject;
      }
    });
  } else {
    mergedResults = zeroValueObjects;
  }

  return mergedResults;
}

/**
 * @param {string} from
 * @param {string} to
 * @returns {object}
 */

function generateZeroValueObjects(from, to) {
  let dateArray = [];
  let currentDate = new Date(from);
  let currentDateString = currentDate.toISOString().split('T')[0];

  const dateRangeType = identifyDateRange(from, to);

  while (currentDate <= new Date(to)) {
    switch (dateRangeType) {
      case 'day':
        currentDateString = currentDate.toISOString().split('T')[0];
        break;
      case 'month':
        currentDateString = currentDate.toLocaleString('default', { month: 'long' });
        break;
      case 'year':
        currentDateString = currentDate.toISOString().split('T')[0].slice(0, 4);
        break;
      default:
        currentDateString = currentDate.toISOString().split('T')[0];
    }

    dateArray.push({
      day: currentDateString,
      revenue: 0,
      total: 0,
      confirmed: 0,
    });

    switch (dateRangeType) {
      case 'day':
        currentDate.setDate(currentDate.getDate() + 1);
        break;
      case 'month':
        currentDate.setMonth(currentDate.getMonth() + 1);
        break;
      case 'year':
        currentDate.setFullYear(currentDate.getFullYear() + 1);
        break;
      default:
        currentDate.setDate(currentDate.getDate() + 1);
    }
  }

  return dateArray;
}

function identifyDateRange(from, to) {
  const diff = new Date(to) - new Date(from);
  const diffInMonths = diff / (1000 * 60 * 60 * 24 * 31);
  const diffInYears = diff / (1000 * 60 * 60 * 24 * 365);

  if (diffInMonths < 1) {
    return 'day';
  } else if (diffInYears < 1) {
    return 'month';
  } else {
    return 'year';
  }
}

/**
 * @param {string} propertyCode
 * @param {string} from
 * @param {string} to
 * @returns {object}
 *  Athena query to get property stats for RateParity
 */

function buildAthenaQuery({ propertyCode, from, to }) {
  // Change date format base on the from and to date difference
  // to %Y-%m-%d if the difference is less than 1 month
  // else %M if the difference is less than 1 year
  // else %Y if the difference is more than 1 year

  const DATE_FORMAT = (() => {
    switch (identifyDateRange(from, to)) {
      case 'day':
        return '%Y-%m-%d';
      case 'month':
        return '%M';
      case 'year':
        return '%Y';
      default:
        return '%Y-%m-%d';
    }
  })();

  let query = `SELECT DATE_FORMAT(qo.createdat, '${DATE_FORMAT}') AS day,
  CAST(
    COALESCE(
      ROUND(
        SUM(
          CASE
            WHEN qo.state = 'confirmed' 
            AND qo.accepted = true 
            THEN qo.price / 100 ELSE 0
          END
        ),
        2
      ),
      0
    ) AS REAL
  ) AS revenue,
  CAST(COALESCE(COUNT(DISTINCT qo.id), 0) AS INTEGER) AS total,
  CAST(
    COALESCE(
      SUM(
        CASE
          WHEN qo.state = 'confirmed' 
          AND qo.accepted = true
          AND qo.reservationid IS NOT NULL 
          THEN 1 ELSE 0
        END
      ),
      0
    ) AS INTEGER
  ) AS confirmed
FROM AwsDataCatalog.rateparity.quote_offers qo
WHERE qo.propertycode = '${propertyCode}'
AND DATE(qo.createdat) BETWEEN DATE('${from}') AND DATE('${to}')
GROUP BY DATE_FORMAT(qo.createdat, '${DATE_FORMAT}')
ORDER BY DATE_FORMAT(qo.createdat, '${DATE_FORMAT}');`;

  return query;
}

module.exports = { getInsightsForRateparity };
