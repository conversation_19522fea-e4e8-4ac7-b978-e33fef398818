const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.type
 * @returns {Promise<QRate>}
 */
function getTemplates({ accountName, language, propertyCode, type }) {
  return invokeService('properties-service', 'getTemplates', {
    accountName,
    language,
    propertyCode,
    type,
  });
}

module.exports = { getTemplates };
