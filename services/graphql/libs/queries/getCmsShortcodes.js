const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.templateId
 * @returns {Promise<QRate>}
 */
function getCmsShortcodes({ accountName, language, propertyCode, templateId }) {
  return invokeService('properties-service', 'getCmsShortcodes', {
    accountName,
    language,
    propertyCode,
    templateId,
  });
}

module.exports = { getCmsShortcodes };
