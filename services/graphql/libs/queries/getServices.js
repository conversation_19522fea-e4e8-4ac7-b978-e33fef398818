const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.roomCode
 * @param {string} arguments.rateId
 * @returns {Promise<QRate>}
 */
function getServices({ accountName, language, propertyCode, roomCode, rateId }) {
  return invokeService('integrations', 'getServices', {
    accountName,
    language,
    propertyCode,
    roomCode,
    rateId,
  });
}

module.exports = { getServices };
