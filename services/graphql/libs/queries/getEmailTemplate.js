const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {string} accountName
 * @param {string} language
 * @param {string} propertyCode
 * @param {string} theme
 * @param {string} id
 * @returns {Promise<QRate>}
 */
function getEmailTemplate({ accountName, language, propertyCode, id, theme }) {
  return invokeService('properties-service', 'getEmailTemplate', {
    accountName,
    language,
    propertyCode,
    id,
    type: 'emailTemplates',
    theme,
  });
}

module.exports = { getEmailTemplate };
