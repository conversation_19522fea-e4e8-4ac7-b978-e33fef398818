const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {Object} args
 * @param {string} args.accountName
 * @param {string} args.propertyCode
 * @param {string} args.accommodationCode
 * @param {string} args.dateFrom             // created at "from" date
 * @param {string} args.dateTo               // created at "to" date
 * @param {string} args.stayFrom
 * @param {string} args.stayTo
 * @returns {Promise<QRate>}
 */
function getReservations(args) {
  return invokeService('integrations', 'getReservations', args);
}

module.exports = { getReservations };
