const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.propertyCode
 * @param {string} arguments.boardCode
 * @returns {Promise<QRate>}
 */
function getBoard({ accountName, propertyCode, language, boardCode }) {
  return invokeService('properties-service', 'getBoard', {
    accountName,
    propertyCode,
    language,
    boardId: boardCode,
  });
}

module.exports = { getBoard };
