const { invokeService } = require('./../utils/invokeLambda');
const { buildElasticSearchDSL } = require('./getRequests');

/**
 * @param {object} args
 * @param {object} context
 * @param {ACL} context.acl
 *
 * @returns {Promise<any>}
 */
function getRequestsStats(args, context) {
  return invokeService('indexing', 'search', {
    indexName: 'newrequests',
    body: buildElasticSearchStatsDSL({ acl: context.acl }),
  }).then(response => createResponse(response));
}

/**
 * Creates the response object.
 * @param {object} response
 * @returns {object}
 */
function createResponse(response) {
  const { aggregations } = response || {};
  const { markedToFollowUpCount, stateCount } = aggregations || {};

  const statKeys = [
    'markedToFollowUp',
    'sent',
    'expired',
    'pending',
    'accepted',
    'opened',
    'confirmed',
    'archived',
    'draft',
    'cancelled',
    'new',
  ];

  let res = statKeys.reduce((acc, key) => {
    acc[key] = 0;
    return acc;
  }, {});

  res['markedToFollowUp'] = markedToFollowUpCount ? markedToFollowUpCount.doc_count : 0;

  res = (stateCount ? stateCount.buckets : []).reduce((res, bucket) => {
    res[bucket.key] = bucket.doc_count;
    return res;
  }, res);

  return res;
}

/**
 * @param {ACL} acl
 *
 * @returns {object}
 */
function buildElasticSearchStatsDSL({ acl }) {
  const body = buildElasticSearchDSL({ acl }); // build the requests index DSL

  delete body.from;
  delete body.size;
  delete body.sort;

  body.aggs = {
    stateCount: {
      terms: {
        field: 'state',
      },
    },
    markedToFollowUpCount: {
      filter: {
        bool: {
          must_not: [{ match: { state: 'archived' } }],
          should: [
            { exists: { field: 'activities.markedToFollowUp' } },
            { exists: { field: 'markedToFollowUp' } },
          ],
          minimum_should_match: 1,
        },
      },
    },
  };
  return body;
}

module.exports = { getRequestsStats, createResponse };
