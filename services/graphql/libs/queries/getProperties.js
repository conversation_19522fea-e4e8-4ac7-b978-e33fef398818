const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {string} accountName
 * @param {string} language
 * @returns {Promise<QProperty[]>}
 */
function getProperties({ accountName, language }) {
  if (!accountName || !language) {
    return Promise.resolve([]);
  }

  return invokeService('properties-service', 'getProperties', {
    accountName,
    language,
  });
}

/**
 * @param {QProperty[]} properties
 * @param {ACL|null} acl
 * @returns {array}
 */
function propertiesPermissionFilter(properties, acl) {
  if (acl) {
    return properties.filter(property => {
      return (
        acl.hasPropertyPermission(property.code, 'create') ||
        acl.hasPropertyPermission(property.code, 'edit')
      );
    });
  } else {
    // There are no permissions defined, internal usage.
    return properties;
  }
}

module.exports = { getProperties, propertiesPermissionFilter };
