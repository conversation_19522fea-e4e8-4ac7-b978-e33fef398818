const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.roomCode
 * @param {string} arguments.rateId
 * @param {string} arguments.serviceId
 * @returns {Promise}
 */
function getAvailability({
  accountName,
  propertyCode,
  checkIn,
  nights,
  adults,
  children,
  infants,
  rooms,
  accommodationCode,
  rate,
  board,
  country,
}) {
  return invokeService('integrations', 'getAvailability', {
    accountName,
    propertyCode,
    checkin: checkIn,
    nights,
    adults,
    children,
    infants,
    rooms,
    accommodationCode,
    rate,
    board,
    country,
  });
}

module.exports = { getAvailability };
