const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * Fetch DEFAULT account settings and Property's
 * and merge them to return
 *
 * @param {string} accountName
 * @param {string} language
 * @param {string} propertyCode
 *
 * @returns {Promise<object>}
 */
function getPropertySettings({ accountName, language, propertyCode }) {
  return invokeService('properties-service', 'getPropertySettings', {
    accountName,
    language,
    propertyCode,
  }).then(response =>
    response.reduce((result, current) => {
      result[current.code] = current.data;
      return result;
    }, {})
  );
}

module.exports = { getPropertySettings };
