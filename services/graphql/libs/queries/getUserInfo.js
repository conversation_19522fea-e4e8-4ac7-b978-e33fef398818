const crypto = require('crypto');
const { invokeService } = require('./../utils/invokeLambda');

const { INTERCOM_SECRET } = process.env;

/**
 * @param {String} operatorId
 * @param {String} accountName
 *
 * @returns {Promise<any>}
 */
function getUserInfo({ operatorId, accountName }) {
  return invokeService('operators-service', 'getOperatorPreferences', {
    operatorId,
    accountName,
  }).then(response => {
    const hmac = crypto.createHmac('sha256', INTERCOM_SECRET);
    hmac.update(operatorId);
    const intercomHash = hmac.digest('hex');

    return { intercomHash, preferences: response, operatorId, accountName };
  });
}

module.exports = { getUserInfo };
