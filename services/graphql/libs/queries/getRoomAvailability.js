const { invokeService } = require('@quotelier/invokeLambda');

/**
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.propertyCode
 * @param {string} arguments.checkIn
 * @param {string} arguments.nights
 * @param {string} arguments.country
 *
 * @returns {Promise}
 */
function getRoomAvailability({ accountName, propertyCode, checkIn, nights, country }) {
  return invokeService('workflows', 'execute', {
    flow: 'groupRequestRoomAvailability',
    identity: accountName,
    wait: true,
    input: {
      accountName,
      propertyCode,
      checkin: checkIn,
      nights,
      country,
    },
  });
}

module.exports = { getRoomAvailability };
