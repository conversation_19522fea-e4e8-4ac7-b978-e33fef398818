const { invokeService } = require('@quotelier/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.email
 * @returns {Promise<any>}
 */
function getEmailStatus({ accountName, email }) {
  return invokeService('notifications', 'blacklistIsListed', {
    accountName,
    email,
  }).then(isBlacklisted => ({ isBlacklisted }));
}

module.exports = { getEmailStatus };
