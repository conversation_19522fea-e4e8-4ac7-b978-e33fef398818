const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.propertyCode
 * @param {string} arguments.rate
 * @param {string} arguments.checkin
 * @param {string} arguments.nights
 * @param {string} arguments.adults
 * @param {string} arguments.children
 * @param {string} arguments.infants
 * @param {string} arguments.rooms
 * @param {string} arguments.country
 * @returns {Promise}
 */
function getServicesAvailability({
  accountName,
  propertyCode,
  checkIn,
  nights,
  adults,
  children,
  infants,
  rooms,
  rate,
  country,
}) {
  return invokeService('integrations', 'getServicesAvailability', {
    accountName,
    propertyCode,
    checkin: checkIn,
    nights,
    adults,
    children,
    infants,
    rooms,
    rateId: rate,
    country,
  });
}

module.exports = { getServicesAvailability };
