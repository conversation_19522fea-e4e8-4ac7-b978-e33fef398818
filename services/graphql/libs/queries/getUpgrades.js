const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {object} args
 * @param {boolean} args.onlyOwned
 * @param {string} args.term
 * @param {string} args.propertyCode
 * @param {string} args.state
 * @param {object} context
 * @param {ACL} context.acl
 *
 * @returns {Promise<any>}
 */
function getUpgrades(args, { acl }) {
  return invokeService('indexing', 'search', {
    indexName: 'upgrades',
    body: buildElasticSearchDsl(args, acl),
  }).then(response => response.hits);
}

/**
 * @param {object} args
 * @param {boolean} args.onlyOwned
 * @param {string} args.term
 * @param {string} args.propertyCode
 * @param {string} args.state
 * @param {ACL} acl
 *
 * @returns {object}
 */
function buildElasticSearchDsl({ onlyOwned, term, propertyCode, state }, acl) {
  const { accountName, operatorId } = acl.access;

  let body = {
    from: 0,
    size: 30,
    sort: [],
    query: {
      bool: {
        must: [{ match: { 'details.accountName.keyword': accountName } }],
        must_not: [],
      },
    },
  };
  /**
   * PROPERTY PERMISSION SETUP
   */
  body.query.bool.must.push({
    bool: {
      should: getAllowedProperties(acl.access.permissions)
        .map(propertyCode => {
          return { match: { 'details.propertyCode.keyword': propertyCode } };
        })
        .concat([{ match: { 'details.operatorId.keyword': operatorId } }]),
      minimum_should_match: 1,
    },
  });

  /**
   * CUSTOM FILTERING
   */
  if (term && term !== '') {
    body.sort.push('_score');
    body.query.bool.should = [
      { match: { 'contact.name': term } },
      { match: { 'contact.nickname': term } },
      { match: { 'contact.email': term } },
      { match: { 'contact.phone': term } },
      { match: { id: term } },
    ];
    body.query.bool.minimum_should_match = 1;
  } else {
    body.sort.push({ 'activities.updated.keyword': { order: 'desc' } });
  }

  if (onlyOwned) {
    body.query.bool.must.push({ match: { 'details.operatorId.keyword': operatorId } });
  }
  if (state) {
    if (state === 'archived') {
      body.query.bool.must.push({ match: { 'state.keyword': 'archived' } });
    } else if (state !== 'all') {
      body.query.bool.must.push({ match: { 'state.keyword': state } });
    } else if (state === 'all') {
      body.query.bool.must_not.push({ match: { 'state.keyword': 'archived' } });
    }
  }
  if (propertyCode) {
    body.query.bool.must.push({ match: { 'details.propertyCode.keyword': propertyCode } });
  }
  return body;
}

/**
 * @param {Array<object<string, string>>} permissions
 * @returns {Array<string>}
 */
function getAllowedProperties(permissions) {
  return permissions
    .filter(permission => {
      return permission[Object.keys(permission)[0]] === 'index';
    })
    .map(permission => Object.keys(permission)[0]);
}

module.exports = { getUpgrades };
