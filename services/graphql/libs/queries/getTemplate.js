const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.type
 * @param {string} arguments.id
 * @returns {Promise<QRate>}
 */
function getTemplate({ accountName, language, propertyCode, type, id }) {
  return invokeService('properties-service', 'getTemplate', {
    accountName,
    language,
    propertyCode,
    id,
    type,
  });
}

module.exports = { getTemplate };
