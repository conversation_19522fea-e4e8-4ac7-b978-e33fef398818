const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {string} accountName
 * @param {string} language
 * @param {string} propertyCode
 * @returns {Promise<Array<object>>}
 */
function getEmailThemes({ accountName, language, propertyCode }) {
  return invokeService('properties-service', 'getEmailThemes', {
    accountName,
    language,
    propertyCode,
  }).then(response => response.map(theme => ({ id: theme })));
}

module.exports = { getEmailThemes };
