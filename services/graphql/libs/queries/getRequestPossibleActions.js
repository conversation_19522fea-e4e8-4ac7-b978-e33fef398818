const { invokeService } = require('./../utils/invokeLambda');
const { getRequest } = require('./getRequest');

/**
 * @param {Object} args
 * @param {string} args.requestId
 * @param {Object} context
 * @param {ACL} context.acl
 * @returns {*}
 */
function getRequestPossibleActions({ requestId }, context) {
  return getRequest({ requestId }).then(request => {
    return invokeService('new-request', 'getRequestPossibleActions', {
      id: requestId,
    }).then(response => response.filter(action => context.acl.canPerformAction(request, action)));
  });
}

module.exports = { getRequestPossibleActions };
