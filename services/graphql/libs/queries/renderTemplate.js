const { invokeService } = require('./../utils/invokeLambda');
const { ValidationError } = require('../errors');

/**
 *
 * @param {Object} args
 * @returns {Promise<QRate>}
 */
function renderTemplate(args) {
  return invokeService('new-request', 'renderTemplate', args).catch(err => {
    // transform invocation error to validation error and show the message to client.
    throw new ValidationError(err.message);
  });
}

module.exports = { renderTemplate };
