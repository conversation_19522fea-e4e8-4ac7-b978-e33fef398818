const { invokeService } = require('./../utils/invokeLambda');
const queryHasSelection = require('./../utils/queryHasSelection');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.upsaleId
 * @param {string} arguments.withPopulations
 * @returns {Promise<QRequest>}
 */
function getUpsale({ upsaleId, withPopulations }) {
  const params = {
    id: upsaleId,
  };

  if (withPopulations) {
    params.with = withPopulations;
  }
  return invokeService('upsales', 'getUpsale', params).then(response =>
    Object.assign({}, response, { emailTemplate: response.template })
  );
}

/**
 * @param {string} upsaleId
 * @param {GqlAST} AST
 * @returns {Promise<QUpsale>}
 */
function getUpsaleFromAST({ upsaleId, AST }) {
  const withPopulations = [];
  if (queryHasSelection(AST, `${AST.fieldName}.operator`)) {
    withPopulations.push('operator');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.request.property`)) {
    withPopulations.push('property');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.rate`)) {
    withPopulations.push('rates');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.accommodation`)) {
    withPopulations.push('accommodations');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.boards`)) {
    withPopulations.push('boards');
  }
  if (queryHasSelection(AST, `${AST.fieldName}.offers.services`)) {
    withPopulations.push('services');
  }

  return getUpsale({ upsaleId, withPopulations });
}

module.exports = { getUpsale, getUpsaleFromAST };
