const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.operatorId
 * @param {string} arguments.language
 * @returns {Promise<QRate>}
 */
function getOperator({ accountName, operatorId, language }) {
  return invokeService('operators-service', 'getOperator', {
    accountName,
    operatorId,
    language,
  });
}

module.exports = { getOperator };
