const jwt = require('jsonwebtoken');

/**
 * @param {String} token
 *
 * @returns {Promise<any>}
 */
function refreshToken(token) {
  const secretKey = process.env.HMAC;

  /* @see: https://gist.github.com/ziluvatar/a3feb505c4c0ec37059054537b38fc48 */
  const payload = jwt.verify(token, secretKey);

  delete payload.iat;
  delete payload.exp;
  delete payload.nbf;
  delete payload.jti;

  return {
    token: jwt.sign(payload, secretKey, {
      expiresIn: '8h',
    }),
  };
}

module.exports = { refreshToken };
