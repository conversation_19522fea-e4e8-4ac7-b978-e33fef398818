const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {string} accountName
 * @param {string} language
 * @param {string} propertyCode
 *
 * @returns {Promise<QProperty>}
 */
function getProperty({ accountName, language, propertyCode }) {
  return invokeService('properties-service', 'getProperty', {
    accountName,
    language,
    propertyCode,
  });
}

module.exports = { getProperty };
