const { invokeService } = require('./../utils/invokeLambda');

const normalizePayload = require('./../utils/normalizePayload');

/**
 *
 * @param {Object} arguments
 * @param {QPrimitiveRequest} arguments.request
 * @returns {Promise<QRate>}
 */
function getRequestPreview({ request }) {
  if (request.attributes && typeof (request.attributes) === "string") {
    request.attributes = JSON.parse(request.attributes);
  }

  return invokeService('new-request', 'renderRequest', normalizePayload(request), {
    previewMode: true,
  });
}

module.exports = { getRequestPreview };
