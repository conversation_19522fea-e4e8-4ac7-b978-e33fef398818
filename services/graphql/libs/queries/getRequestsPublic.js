const { invokeService } = require('./../utils/invokeLambda');
const deprecationSupport = require('../utils/requestDeprecationSupport');

/**
 * @param {object} args
 * @param {string} args.propertyCode
 * @param {string} args.channel
 * @param {object} context
 * @param {ACL} context.acl
 *
 * @returns {Promise<any>}
 */
function getRequestsPublic(args, context) {
  return invokeService('indexing', 'search', {
    indexName: 'newrequests',
    body: buildElasticSearchDSL(Object.assign(args)),
  }).then((response) => response.hits.map((request) => deprecationSupport(request)));
}

/**
 * @param {string} propertyCode
 * @param {string} channel
 *
 * @returns {object}
 */
function buildElasticSearchDSL({ propertyCode, channel }) {
  let body = {
    from: 0,
    size: 30,
    sort: [],

    /**
     * Exclude all nested properties with the name "photos".
     * @todo: Remove this exclusion once all request with the heavy property-object are expired.
     * @see https://headinbeds.atlassian.net/browse/QSLS-772
     */
    _source: {
      exclude: ['*.photos'],
    },
    query: {
      bool: {
        must: [{ match: { 'request.propertyCode.keyword': propertyCode } }],
        must_not: [],
      },
    },
  };

  if (channel) {
    body.query.bool.must.push({ match: { 'request.channel.keyword': channel } });
  }

  body.query.bool.must_not.push({ match: { 'state.keyword': 'archived' } });
  body.query.bool.must_not.push({ match: { 'state.keyword': 'expired' } });
  body.query.bool.must_not.push({ match: { 'contact.wiped': true } });

  body.sort.push({ 'activities.updated': { order: 'desc' } });
  body.sort.push({ updatedAt: { order: 'desc' } });

  return body;
}

module.exports = { getRequestsPublic };
