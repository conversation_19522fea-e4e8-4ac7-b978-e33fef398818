const { invokeService } = require('./../utils/invokeLambda');

/**
 * @param {string} accountName
 * @param {string} language
 * @param {string} propertyCode
 * @param {string} [theme]
 * @returns {Promise<Array<object>>}
 */
function getEmailTemplates({ accountName, language, propertyCode, theme }) {
  return invokeService('properties-service', 'getEmailTemplates', {
    accountName,
    language,
    propertyCode,
    theme,
  });
}

module.exports = { getEmailTemplates };
