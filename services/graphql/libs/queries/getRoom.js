const { invokeService } = require('./../utils/invokeLambda');

/**
 *
 * @param {Object} arguments
 * @param {string} arguments.accountName
 * @param {string} arguments.language
 * @param {string} arguments.propertyCode
 * @param {string} arguments.roomCode
 * @returns {Promise<QRate>}
 */
function getRoom({ accountName, language, propertyCode, roomCode }) {
  return invokeService('integrations', 'getRoom', {
    accountName,
    language,
    propertyCode,
    roomCode,
  });
}

module.exports = { getRoom };
