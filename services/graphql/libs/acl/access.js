'use strict';
const BaseJWTAccessControl = require('@quotelier/graphql-access-control').JWTAccessControl;
const { getRequest } = require('./../queries');
const { maskErrors } = require('graphql-errors');
const { ForbiddenError, isUserError } = require('./../errors');

/**
 * Customize and override the JWTAccessControl deny handler.
 */
class JWTAccessControl extends BaseJWTAccessControl {
  /**
   * Override the deny-event handler.
   * @override
   * @param {string?} message
   * @returns {void}
   */
  denyHandler(message) {
    throw new ForbiddenError(message || 'Forbidden');
  }
}

/**
 * @param {GraphQLSchema} schema
 * @returns {GraphQLSchema}
 */
function decorate(schema) {
  const accessControl = new JWTAccessControl(schema, process.env.HMAC);

  /**
   * accessControl.check handler:
   *  returns true = allow access
   *  returns false = deny access
   *  returns string = deny access with custom message
   */

  /**
   * Check the account membership and the action permission.
   */
  accessControl.check(
    ['mutation.performRequestAction', 'mutation.muteToggle'],
    (jwtData, source, args, { acl }) => {
      return getRequest({ requestId: args.requestId }).then(request => {
        if (args.action && !acl.canPerformAction(request, args.action)) {
          return `You are not allowed to perform action ${args.action}`; // deny with message
        } else if (
          !acl.hasPropertyPermission(request.request.propertyCode, 'edit') &&
          !acl.isOwnerOfRequest(request)
        ) {
          return false; // deny
        } else {
          return true; //  allow
        }
      });
    }
  );

  /**
   * Check the account membership and property assign permission.
   */
  accessControl.check('mutation.assignOperator', (jwtData, source, args, { acl }) => {
    return getRequest({ requestId: args.requestId }).then(request => {
      if (!acl.hasPropertyPermission(request.request.propertyCode, 'assign')) {
        return 'You are not allowed to assign operators.';
      } else {
        return true;
      }
    });
  });

  /**
   * Check the permission "create" and the account access.
   */
  accessControl.check(
    ['mutation.createRequest', 'query.requestSummary', 'mutation.createWaitingRequest'],
    (jwtData, source, args, { acl }) => {
      if (!acl.hasPropertyPermission(args.request.request.propertyCode, 'create')) {
        return `You are not allowed to create request for property "${
          args.request.request.propertyCode
        }"`;
      } else {
        return true; // allow access, pass to resolver
      }
    }
  );

  /**
   * Check the permission "edit", access account and request state.
   */
  accessControl.check(['mutation.updateRequest'], (jwtData, source, args, { acl }) => {
    if (!acl.hasPropertyPermission(args.request.request.propertyCode, 'edit')) {
      return `You are not allowed to update request of property "${
        args.request.request.propertyCode
      }"`;
    } else {
      return getRequest({ requestId: args.request.id }).then(({ state }) => {
        return state === 'draft' ? true : 'Request is not editable.';
      });
    }
  });

  /**
   * Check the account and the property read access.
   */
  accessControl.check(
    [
      'query.account',
      'query.accountLanguages',
      'query.indexProperties',
      'query.properties',
      'query.property',
      'query.room',
      'query.rooms',
      'query.rates',
      'query.rate',
      'query.services',
      'query.service',
      'query.template',
      'query.templates',
      'query.operator',
      'query.operators',
      'query.availability',
    ],
    (jwtData, source, args, { acl }) => {
      if (args.propertyCode && !acl.hasPropertyPermission(args.propertyCode, 'create')) {
        return `You are not allowed to perform this query on property "${args.propertyCode}"`;
      } else {
        return true;
      }
    }
  );

  /**
   * Access control check for the CMSystem.
   */
  accessControl.check(
    ['query.emailTemplates', 'mutation.saveEmailTemplate', 'mutation.resetEmailTemplate'],
    (jwtData, source, args, { acl }) => {
      const propertyCode = args.propertyCode || (args.template ? args.template.propertyCode : null);
      if (propertyCode && !acl.hasPropertyPermission(propertyCode, 'cms')) {
        return `You are not allowed to perform this query on property "${propertyCode}"`;
      } else {
        return true;
      }
    }
  );
  accessControl.decorator.decorateMultiple('*', (resolver, source, args, context, ast) => {
    return Promise.resolve()
      .then(() => resolver(source, args, context, ast))
      .catch(error => {
        if (!isUserError(error)) {
          console.log(JSON.stringify({ ResolverError: true, error }));
        }
        throw error;
      });
  });
  return maskErrors(accessControl.getSchema());
}

module.exports = {
  decorate,
};
