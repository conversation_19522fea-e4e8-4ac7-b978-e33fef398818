const { verify } = require('jsonwebtoken');

const { HMAC } = process.env;

/**
 * @param {string} bearer
 * @returns {Object} JSON
 */
function verifyJWT(bearer) {
  const grp = /Bearer\s+([\w.-]+)/.exec(bearer);
  let jwtData;
  if (!grp || grp.length !== 2) {
    throw new Error('Invalid authentication header');
  } else {
    try {
      jwtData = verify(grp[1], HMAC);
    } catch (err) {
      throw new Error(err.message);
    }
  }
  return jwtData;
}

module.exports = { verifyJWT };
