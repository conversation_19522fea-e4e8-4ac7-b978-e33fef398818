const { getActionPermissions } = require('./../utils/requestActions');

/**
 * Represents the unit to check if the current session based on JWT data
 * are allowed to perform actions or belong to specific roles.
 */
class ACL {
  /**
   * @param {object} access
   * @param {string} access.operatorId
   * @param {string} access.accountName
   * @param {object<string, string>} access.permissions
   */
  constructor(access) {
    this.access = access;
  }

  /**
   * Check the account membership of the current session.
   *
   * @param {string} accountName
   * @returns {boolean}
   */
  belongsToAccount(accountName) {
    return this.access.accountName === accountName;
  }

  /**
   * Check if the current session has permission role to a specific property.
   *
   * @param {string} property
   * @param {'create'|'index'|'edit'|'own'|'assign'|'delete'} action
   * @returns {boolean}
   */
  hasPropertyPermission(property, action) {
    return (
      this.access.permissions
        .map(permission => {
          return permission[property] && permission[property] === action;
        })
        .indexOf(true) !== -1
    );
  }

  /**
   * @param {QRequest} request
   * @returns {boolean} the current user is the owner of the request
   */
  isOwnerOfRequest(request) {
    return this.access.operatorId === request.operatorId;
  }

  /**
   * Check if the current session is allowed to perform an action such as
   * "archive", "confirm" etc.
   *
   * @param {QRequest} request
   * @param {string} action
   * @returns {boolean}
   */
  canPerformAction(request, action) {
    const permissions = getActionPermissions(action);
    if (!permissions) {
      return false;
    }
    const canBasedOnProperty =
      permissions
        .map(per => this.hasPropertyPermission(request.request.propertyCode, per))
        .indexOf(true) !== -1;

    if (action === 'archive' && !canBasedOnProperty) {
      return false;
    }

    if (canBasedOnProperty && action === 'own' && !request.operatorId) {
      return true;
    }

    return canBasedOnProperty || this.isOwnerOfRequest(request);
  }
}

module.exports = { ACL };
