const { getUpsale } = require('./../queries');
const { invokeService } = require('./../utils/invokeLambda');

const actionsToLambdaMap = {
  send: 'sendUpsale',
  confirm: 'confirmUpsale',
  archive: 'archiveUpsale',
};

/**
 * @param {Object} args
 * @param {string} args.action
 * @param {string} args.upsaleId
 * @returns {Promise}
 */
function performUpsaleAction(args) {
  const action = args.action;
  const upsaleId = args.upsaleId;

  const payload = { upsaleId };

  return invokeService('upsales', actionsToLambdaMap[action], payload).then(() =>
    getUpsale({ upsaleId })
  );
}

module.exports = { performUpsaleAction };
