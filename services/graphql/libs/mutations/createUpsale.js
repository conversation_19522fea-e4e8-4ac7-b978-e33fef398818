const { invokeService } = require('./../utils/invokeLambda');
const { getUpsaleFromAST } = require('./../queries/getUpsale');
const normalizePayload = require('./../utils/normalizePayload');

/**
 *
 * @param {{upsale: QUpsale}} args
 * @param {Object} ast
 * @returns {Promise<void>}
 */
function createUpsale({ upsale }, ast) {
  return invokeService('upsales', 'createUpsale', normalizePayload(upsale)).then(response =>
    getUpsaleFromAST({ upsaleId: response.id, AST: ast })
  );
}

module.exports = {
  createUpsale,
};
