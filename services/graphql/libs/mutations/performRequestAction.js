const { getRequest } = require('./../queries');
const { invokeService } = require('./../utils/invokeLambda');
const { UserError } = require('./../errors');

const requestToLambdaMap = {
  own: 'ownRequest',
  followup: 'followupRequest',
  expire: 'expireRequest',
  acceptRequestWithOffer: 'acceptRequestOffer',
  acceptRequestWithOptions: 'acceptRequest',
  extend: 'extendRequest',
  confirm: 'confirmRequest',
  revert: 'revertRequest',
  cancel: 'cancelRequest',
  archive: 'archiveRequest',
  acceptUnavailable: 'acceptUnavailableRequest',
  cancelUnavailable: 'cancelUnavailableRequest',
  waitlist: 'waitlistRequest',
};

/**
 * @param {string} action
 * @returns {string}
 */
function getLambdaToCall(action) {
  if (!(action in requestToLambdaMap)) {
    throw new UserError('Action is not defined');
  }

  return requestToLambdaMap[action];
}

/**
 * @param {Object} args
 * @param {string} args.action
 * @param {string} args.requestId
 * @param {string} args.date In case of Extend, date should be sent along with the requestId
 * @param {string} args.offerId In case of Accept, the offerId that you want to accept
 * @param {string} args.optionId In case of Accept, the optionId that you want to accept
 * @param {string} args.silent Override Request muted state
 * @param {Object} context
 * @returns {Promise}
 */
function performRequestAction(args, context) {
  let action = args.action;

  const { requestId, date, offerId, optionId, optionIds, silent } = args;

  const payload = { requestId };

  if (action === 'extend' && date) {
    payload.date = date;
  }

  if (action === 'own' || action === 'waitlist') {
    payload.operatorId = context.acl.access.operatorId;
  }

  if (action === 'accept') {
    if (!isNaN(parseFloat(offerId))) {
      payload.offerId = offerId;
      action = 'acceptRequestWithOffer';
    } else if (optionId !== undefined) {
      payload.optionIds = [optionId];
      action = 'acceptRequestWithOptions';
    } else if (optionIds !== undefined && optionIds.length) {
      payload.optionIds = optionIds;
      action = 'acceptRequestWithOptions';
    } else {
      throw new UserError('offerId(Int) or optionId(String) or optionIds(String[]) is required');
    }
  }

  return invokeService('new-request', getLambdaToCall(action), payload, { silent: !!silent }).then(
    () => getRequest({ requestId })
  );
}

module.exports = { performRequestAction };
