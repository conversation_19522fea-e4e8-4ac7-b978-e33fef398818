const pascalcase = require('pascalcase');
const { invokeService } = require('./../utils/invokeLambdaNative');

/**
 * @param {{action: string, upgradeId: string}} args
 * @param {{acl: ACL}} context
 * @returns {Promise}
 */
function performUpgradeAction(args, context) {
  const { action, upgradeId } = args;
  const { accountName, propertyCode } = context.acl;

  return invokeService('workflows', 'execute', {
    executionId: `upgrade-${upgradeId}-${action}`,
    identity: `${accountName}-${propertyCode}`,
    flow: `upgradeAction${pascalcase(action)}`,
    input: { upgradeId },
  });
}

module.exports = { performUpgradeAction };
