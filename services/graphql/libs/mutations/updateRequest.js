const { invokeService } = require('./../utils/invokeLambda');
const { invokeServiceAsync } = require('@quotelier/invokeLambda');
const normalizePayload = require('./../utils/normalizePayload');

/**
 *
 * @param {Object} args
 * @param {QRequest} args.request
 *
 * @returns {*}
 */
function updateRequest({ request }) {
  if (request.attributes && typeof (request.attributes) === "string") {
    request.attributes = JSON.parse(request.attributes);
  }

  return invokeService('new-request', 'updateRequest', normalizePayload(request)).then(response =>
    invokeServiceAsync('new-request', 'sendRequest', {
      requestId: response.id,
    }).then(() => Object.assign({}, response, { state: 'pending' }))
  );
}

module.exports = { updateRequest };
