const { invokeService } = require('./../utils/invokeLambda');
const { invokeServiceAsync } = require('@quotelier/invokeLambda');
const normalizePayload = require('./../utils/normalizePayload');
/**
 *
 * @param {{request: QRequest}} args
 * @returns {Promise<void>}
 */
function createRequest({ request }) {
  /**
   * This is actually the createAndSend mutation.
   * @todo Rename this once the create mutation has been added
   */

  if (request.attributes && typeof (request.attributes) === "string") {
    request.attributes = JSON.parse(request.attributes);
  }

  return invokeService('new-request', 'createRequest', normalizePayload(request)).then(response =>
    invokeServiceAsync('new-request', 'sendRequest', {
      requestId: response.id,
    }).then(() => Object.assign({}, response, { state: 'pending' }))
  );
}

module.exports = {
  createRequest,
};
