const { invokeService } = require('./../utils/invokeLambda');
const normalizePayload = require('./../utils/normalizePayload');

/**
 *
 * @param {{id: String, request: QRequest}} args
 * @returns {Promise<void>}
 */
function saveRequest({ id, request }) {
  if (request.attributes && typeof (request.attributes) === "string") {
    request.attributes = JSON.parse(request.attributes);
  }

  if (id) {
    const payload = Object.assign({}, request, { id });
    return invokeService('new-request', 'updateRequest', normalizePayload(payload));
  } else {
    return invokeService('new-request', 'createRequest', normalizePayload(request));
  }
}

module.exports = {
  saveRequest,
};
