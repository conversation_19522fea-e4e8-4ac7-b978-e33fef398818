const { invokeService } = require('./../utils/invokeLambda');

/**
 * Assign an operator to a specific request.
 *
 * @param {string} requestId
 * @param {string} operatorId
 * @returns {Promise<QPrimitiveRequest>}
 */
function assignRequest({ requestId, operatorId }) {
  return invokeService('new-request', 'assignRequest', {
    requestId,
    operatorId,
  });
}

module.exports = { assignRequest };
