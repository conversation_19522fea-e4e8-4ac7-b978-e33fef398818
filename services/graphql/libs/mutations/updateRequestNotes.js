const { invokeService } = require('./../utils/invokeLambda');

/**
 * Update Request notes
 *
 * @param {Object} notes
 * @param {string} notes.id
 * @param {string} notes.notes
 * @param {string} notes.channel
 * @param {string} notes.country
 * @param {string} notes.tags
 * @returns {Promise<QPrimitiveRequest>}
 */
function updateRequestNotes({ notes }) {
  if (notes.attributes && typeof (notes.attributes) === "string") {
    notes.attributes = JSON.parse(notes.attributes);
  }

  return invokeService('new-request', 'updateRequestNotes', notes);
}

module.exports = { updateRequestNotes };
