const { invokeService } = require('./../utils/invokeLambdaNative');

/**
 *
 * @param {{id: string, upgrade: QPrimitiveUpgrade}} args
 * @param {{acl: ACL}} context
 * @returns {Promise<void>}
 */
function saveUpgrade(args, context) {
  const { accountName, operatorId } = context.acl.access;
  Object.assign(args.upgrade.details, { accountName, operatorId });

  return invokeService('quotelier-upgrade', 'updateUpgrade', {
    upgradeId: args.id,
    upgrade: args.upgrade,
  });
}

module.exports = {
  saveUpgrade,
};
