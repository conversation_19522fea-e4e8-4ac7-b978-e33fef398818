'use strict';

const {
  Graph<PERSON>List,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLString,
  GraphQLInt,
  GraphQLBoolean,
} = require('graphql');

const { GraphQLJSON } = require('graphql-type-json');

const { UserError } = require('./errors');

const { populateRates, populateRate } = require('./utils/populators/board');

const { propertiesPermissionFilter } = require('./queries/getProperties');
const {
  getEmailThemes,
  getAccountLanguages,
  getOperator,
  getOperators,
  getOperatorTags,
  getProperties,
  getPropertySettings,
  getProperty,
  getRate,
  getRequests,
  getUpsales,
  getRates,
  getRequestPossibleActions,
  getUpsalePossibleActions,
  getOutboundMessages,
  getRoom,
  getRooms,
  getUpsaleFromAST,
  getRequestFromAST,
  getService,
  getServices,
  getTemplates,
  getTemplate,
  getEmailTemplate,
  getEmailTemplates,
  getAvailability,
  getAvailabilityBreakdown,
  getServicesAvailability,
  getRequestPreview,
  getUpsalePreview,
  getOpportunities,
  getRequestsStats,
  refreshToken,
  getUserInfo,
  getCmsShortcodes,
  renderTemplate,
  getReservations,
  getUpgradeOpportunities,
  getAttributeSchema,
  getUpgrade,
  getUpgrades,
  getUpgradePossibleActions,
  getUpgradePreview,
  getUpgradeStates,
  getEmailStatus,
  getRoomAvailability,
} = require('./queries');

const populate = require('./utils/populate');

const Property = require('./types/Property');
const Room = require('./types/Room');
const Rate = require('./types/Rate');
const CountryCodeResult = require('./types/CountryCodeResult');
const Service = require('./types/Service');
const Operator = require('./types/Operator');
const UserInfoOutput = require('./types/UserInfo');
const Token = require('./types/Token');
const AttributeSchema = require('./types/AttributeSchema');

const {
  Request,
  RequestInput,
  RequestActions,
  RequestStateFilter,
  RequestsStats,
} = require('./types/Request');
const { Upsale, UpsalePreviewInput, UpsaleActions, UpsaleStateFilter } = require('./types/Upsale');
const {
  AvailabilityService,
  AvailabilityOffer,
  AvailabilityBreakdownOffer,
  AvailabilityRoomOffer,
} = require('./types/Availability');
const { PropertySettings } = require('./types/PropertySettings');
const { OutboundMessage } = require('./types/OutboundMessage');
const Opportunity = require('./types/Opportunity');
const EmailStatus = require('./types/EmailStatus');

const {
  Template,
  TemplateData,
  EmailTemplateAssets,
  TemplateType,
  EmailTemplate,
  EmailTheme,
} = require('./types/Template');

const { UpgradeOpportunity, Upgrade, UpgradePreview } = require('./types/Upgrade');

const { Reservation, ReservationStatus } = require('./types/Reservation');

const { CMSShortcode } = require('./types/CMS');

const iso3311a2 = require('iso-3166-1-alpha-2');

/**
 * A little helper to inject the accountName from context
 * to any Object.
 *
 * @param {Object} args
 * @param {Object} context
 * @param {Object} context.acl
 * @param {Object} context.acl.access
 * @param {String} context.acl.access.accountName
 * @returns {Object}
 */
function addAccountNameToArgs(args, context) {
  return Object.assign({}, args, { accountName: context.acl.access.accountName });
}

/**
 * A little helper to inject the accountName from context
 * to a Request input.
 *
 * @param {Object} args
 * @param {Object} context
 * @param {Object} context.acl
 * @param {Object} context.acl.access
 * @param {String} context.acl.access.accountName
 * @returns {Object}
 */
function addAccountNameToRequest(args, context) {
  return Object.assign({}, args, {
    request: Object.assign({}, args.request, {
      request: Object.assign({}, args.request.request, {
        accountName: context.acl.access.accountName,
      }),
    }),
  });
}

/**
 * A little helper to inject the accountName from context
 * to a Upsale input.
 *
 * @param {Object} args
 * @param {Object} context
 * @param {Object} context.acl
 * @param {Object} context.acl.access
 * @param {String} context.acl.access.accountName
 * @returns {Object}
 */
function addAccountNameToUpsale(args, context) {
  return Object.assign({}, args, {
    upsale: Object.assign({}, args.upsale, {
      request: Object.assign({}, args.upsale.request, {
        accountName: context.acl.access.accountName,
      }),
    }),
  });
}

module.exports = new GraphQLObjectType({
  name: 'RootQueries',
  fields: () => ({
    userInfo: {
      type: UserInfoOutput,
      resolve: (source, args, context) =>
        getUserInfo({
          operatorId: context.acl.access.operatorId,
          accountName: context.acl.access.accountName,
        }),
    },
    refreshToken: {
      type: Token,
      resolve: (source, args, context) =>
        refreshToken(context.authorizationHeader.replace('Bearer ', '')),
    },
    upgradeOpportunities: {
      type: new GraphQLList(UpgradeOpportunity),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        accommodationCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        stayFrom: {
          type: new GraphQLNonNull(GraphQLString),
        },
        stayTo: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) =>
        getUpgradeOpportunities(addAccountNameToArgs(args, context)),
    },
    upgrade: {
      type: Upgrade,
      args: {
        upgradeId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getUpgrade(args, context),
    },
    upgrades: {
      type: new GraphQLList(Upgrade),
      args: {
        term: {
          type: GraphQLString,
        },
        onlyOwned: {
          type: GraphQLBoolean,
        },
        propertyCode: {
          type: GraphQLString,
        },
        state: {
          type: GraphQLString,
        },
      },
      resolve: (source, args, context) => getUpgrades(args, context),
    },
    upgradeStates: {
      type: new GraphQLList(GraphQLString),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getUpgradeStates(args, context),
    },
    upgradePossibleActions: {
      type: new GraphQLList(GraphQLString),
      args: {
        upgradeId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getUpgradePossibleActions(args, context),
    },
    upgradePreview: {
      type: new GraphQLNonNull(GraphQLString),
      args: {
        upgrade: {
          type: UpgradePreview,
        },
      },
      resolve: (source, args, context) => getUpgradePreview(args, context),
    },
    opportunities: {
      type: new GraphQLList(Opportunity),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        from: {
          type: new GraphQLNonNull(GraphQLString),
        },
        to: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getOpportunities(addAccountNameToArgs(args, context)),
    },
    accountLanguages: {
      type: new GraphQLList(
        new GraphQLObjectType({
          name: 'AccountLanguage',
          fields: {
            language: {
              type: new GraphQLNonNull(GraphQLString),
            },
            languageName: {
              type: new GraphQLNonNull(GraphQLString),
            },
          },
        })
      ),
      resolve: (source, args, context) => getAccountLanguages(addAccountNameToArgs(args, context)),
    },
    countryCodes: {
      type: new GraphQLList(CountryCodeResult),
      args: {},
      resolve: () =>
        iso3311a2.getCodes().map(code => ({
          code: code,
          countryName: iso3311a2.getCountry(code),
        })),
    },
    tags: {
      type: new GraphQLList(GraphQLString),
      args: {
        operatorId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args) => getOperatorTags(args),
    },
    indexProperties: {
      type: new GraphQLList(Property),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getProperties(addAccountNameToArgs(args, context)),
    },
    properties: {
      type: new GraphQLList(Property),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) =>
        getProperties(addAccountNameToArgs(args, context)).then(properties =>
          propertiesPermissionFilter(properties, context.acl)
        ),
    },
    property: {
      type: Property,
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getProperty(addAccountNameToArgs(args, context)),
    },
    rooms: {
      type: new GraphQLList(Room),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getRooms(addAccountNameToArgs(args, context)),
    },
    room: {
      type: Room,
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getRoom(addAccountNameToArgs(args, context)),
    },
    rates: {
      type: new GraphQLList(Rate),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context, ast) =>
        getRates(addAccountNameToArgs(args, context)).then(rates =>
          populate(ast, rates, {
            'rates.board': () => populateRates(args, rates),
          })
        ),
    },
    rate: {
      type: Rate,
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        rateId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context, ast) =>
        getRate(addAccountNameToArgs(args, context)).then(rate =>
          populate(ast, rate, {
            'rate.board': () => populateRate(args, rate),
          })
        ),
    },
    reservations: {
      type: new GraphQLList(Reservation),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        accommodationCode: {
          type: GraphQLString,
        },
        externalId: {
          type: GraphQLString,
        },
        status: {
          type: ReservationStatus,
        },
        dateFrom: {
          type: GraphQLString,
          description: 'Created at from date',
        },
        dateTo: {
          type: GraphQLString,
          description: 'Created at to date',
        },
        stayFrom: {
          type: GraphQLString,
        },
        stayTo: {
          type: GraphQLString,
        },
      },
      resolve: (source, args, context) => getReservations(addAccountNameToArgs(args, context)),
    },
    services: {
      type: new GraphQLList(Service),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        rateId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getServices(addAccountNameToArgs(args, context)),
    },
    service: {
      type: Service,
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        rateId: {
          type: new GraphQLNonNull(GraphQLString),
        },
        serviceId: {
          type: new GraphQLNonNull(GraphQLInt),
        },
      },
      resolve: (source, args, context) => getService(addAccountNameToArgs(args, context)),
    },
    requestsStats: {
      type: RequestsStats,
      resolve: (source, args, context) => getRequestsStats(args, context),
    },
    requests: {
      type: new GraphQLList(Request),
      args: {
        term: {
          type: GraphQLString,
        },
        onlyOwned: {
          type: GraphQLBoolean,
        },
        propertyCode: {
          type: GraphQLString,
        },
        state: {
          type: RequestStateFilter,
        },
        markedToFollowUp: {
          type: GraphQLBoolean,
        },
      },
      resolve: (source, args, context) => getRequests(args, context),
    },
    request: {
      type: Request,
      args: {
        id: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context, ast) => {
        return getRequestFromAST({ requestId: args.id, AST: ast }).then(response => {
          if (!context.acl.hasPropertyPermission(response.request.propertyCode, 'index')) {
            throw new UserError("You don't have permissions to access this request.");
          }

          return response;
        });
      },
    },
    requestPreview: {
      type: new GraphQLNonNull(GraphQLString),
      args: {
        request: {
          type: RequestInput,
        },
      },
      resolve: (source, args, root, ast) =>
        getRequestPreview(addAccountNameToRequest(args, root), ast),
    },
    requestPossibleActions: {
      type: new GraphQLList(RequestActions),
      args: {
        requestId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getRequestPossibleActions(args, context),
    },
    templates: {
      type: new GraphQLList(Template),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        type: {
          type: new GraphQLNonNull(TemplateType),
        },
      },
      resolve: (source, args, context) => getTemplates(addAccountNameToArgs(args, context)),
    },
    attributeSchema: {
      type: AttributeSchema,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getAttributeSchema(addAccountNameToArgs(args, context)),
    },
    settings: {
      type: PropertySettings,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getPropertySettings(addAccountNameToArgs(args, context)),
    },
    template: {
      type: Template,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        type: {
          type: new GraphQLNonNull(TemplateType),
        },
        id: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getTemplate(addAccountNameToArgs(args, context)),
    },
    emailTemplate: {
      type: EmailTemplate,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        theme: {
          type: GraphQLString,
        },
        id: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getEmailTemplate(addAccountNameToArgs(args, context)),
    },
    emailTemplateAssets: {
      type: EmailTemplateAssets,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => addAccountNameToArgs(args, context),
    },
    emailTemplates: {
      type: new GraphQLList(EmailTemplate),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        theme: {
          type: GraphQLString,
        },
      },
      resolve: (source, args, context) => getEmailTemplates(addAccountNameToArgs(args, context)),
    },
    emailThemes: {
      type: new GraphQLList(EmailTheme),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getEmailThemes(addAccountNameToArgs(args, context)),
    },
    emailStatus: {
      type: EmailStatus,
      args: {
        email: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getEmailStatus(addAccountNameToArgs(args, context)),
    },
    cmsShortcodes: {
      type: new GraphQLList(CMSShortcode),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        templateId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getCmsShortcodes(addAccountNameToArgs(args, context)),
    },
    availability: {
      type: new GraphQLList(AvailabilityOffer),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        checkIn: {
          type: new GraphQLNonNull(GraphQLString),
        },
        nights: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        adults: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        children: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        infants: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        rooms: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        accommodationCode: {
          type: GraphQLString,
        },
        rate: {
          type: GraphQLString,
        },
        board: {
          type: GraphQLInt,
        },
        country: {
          type: GraphQLString,
          description: "User's location expressed in ISO_3166-1_alpha-2 code",
        },
        bookingCode: {
          type: GraphQLString,
          description: 'The booking code of the request, used to retrieve special rates.',
        },
      },
      resolve: (source, args, context) => getAvailability(addAccountNameToArgs(args, context)),
    },
    roomAvailability: {
      type: new GraphQLList(AvailabilityRoomOffer),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        checkIn: {
          type: new GraphQLNonNull(GraphQLString),
        },
        nights: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        country: {
          type: GraphQLString,
          description: "User's location expressed in ISO_3166-1_alpha-2 code",
        },
      },
      resolve: (source, args, context) => getRoomAvailability(addAccountNameToArgs(args, context)),
    },
    availabilityBreakdown: {
      type: new GraphQLList(AvailabilityBreakdownOffer),
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        checkIn: {
          type: new GraphQLNonNull(GraphQLString),
        },
        nights: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        adults: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        children: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        infants: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        rooms: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        accommodationCode: {
          type: GraphQLString,
        },
        rate: {
          type: GraphQLString,
        },
        board: {
          type: GraphQLInt,
        },
        country: {
          type: GraphQLString,
          description: "User's location expressed in ISO_3166-1_alpha-2 code",
        },
        bookingCode: {
          type: GraphQLString,
          description: 'The booking code of the request, used to retrieve special rates.',
        },
      },
      resolve: (source, args, context) =>
        getAvailabilityBreakdown(addAccountNameToArgs(args, context)),
    },
    servicesAvailability: {
      type: new GraphQLList(AvailabilityService),
      deprecated: true,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        checkIn: {
          type: new GraphQLNonNull(GraphQLString),
        },
        nights: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        adults: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        children: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        infants: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        rooms: {
          type: new GraphQLNonNull(GraphQLInt),
        },
        rate: {
          type: new GraphQLNonNull(GraphQLString),
        },
        country: {
          type: GraphQLString,
          description: "User's location expressed in ISO_3166-1_alpha-2 code",
        },
        bookingCode: {
          type: GraphQLString,
          description: 'The booking code of the request, used to retrieve special rates.',
        },
      },
      resolve: (source, args, context) =>
        getServicesAvailability(addAccountNameToArgs(args, context)),
    },
    operators: {
      type: new GraphQLList(Operator),
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getOperators(addAccountNameToArgs(args, context)),
    },
    operator: {
      type: Operator,
      args: {
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        operatorId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getOperator(addAccountNameToArgs(args, context)),
    },
    outboundMessages: {
      type: new GraphQLList(OutboundMessage),
      args: {
        requestId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args) => getOutboundMessages({ requestId: args.requestId }),
    },
    renderTemplate: {
      type: TemplateData,
      args: {
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString),
        },
        language: {
          type: new GraphQLNonNull(GraphQLString),
        },
        theme: {
          type: GraphQLString,
        },
        template: {
          type: new GraphQLNonNull(GraphQLString),
        },
        metadata: {
          type: GraphQLJSON,
        },
      },
      resolve: (source, args, context) => renderTemplate(addAccountNameToArgs(args, context)),
    },
    upsales: {
      type: new GraphQLList(Upsale),
      args: {
        term: {
          type: GraphQLString,
        },
        onlyOwned: {
          type: GraphQLBoolean,
        },
        propertyCode: {
          type: GraphQLString,
        },
        state: {
          type: UpsaleStateFilter,
        },
      },
      resolve: (source, args, context) => getUpsales(args, context),
    },
    upsale: {
      type: Upsale,
      args: {
        id: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, root, ast) => {
        return getUpsaleFromAST({ upsaleId: args.id, AST: ast });
      },
    },
    upsalePreview: {
      type: new GraphQLNonNull(GraphQLString),
      args: {
        upsale: {
          type: UpsalePreviewInput,
        },
      },
      resolve: (source, args, root, ast) =>
        getUpsalePreview(addAccountNameToUpsale(args, root), ast),
    },
    upsalePossibleActions: {
      type: new GraphQLList(UpsaleActions),
      args: {
        upsaleId: {
          type: new GraphQLNonNull(GraphQLString),
        },
      },
      resolve: (source, args, context) => getUpsalePossibleActions(args, context),
    },
  }),
});
