const Joi = require('joi');
const {
  validateEventSchema,
  contact,
  guestDetails,
  requestDetails,
  offer,
  requestOptions,
} = require('../lib/schemas');
const errorHandler = require('./errorHandler');
const { ValidationError } = require('../errors');
const generateRandomRequestId = require('../lib/generateRandomRequestId');
const { putRequest, REQUEST_STATE } = require('../lib/request');
const { scheduleToExpire } = require('../lib/scheduler');
const { resetDates } = require('../lib/resetDates');
const properties = require('../lib/properties');
const assignFlowType = require('../lib/assignFlowType');
const { addTags, getOperator, getOperatorSettings } = require('../lib/operator');
const customAttributes = require('../lib/customAttributes');
const middlewares = require('../middlewares');
const { invokeLambda } = require('@quotelier/invokeLambda');

/**
 * @typedef {object} CreateRequestEvent
 */

/**
 * @typedef {!LambdaResponse.<any>} CreateRequestResponse
 */

const eventSchema = Joi.object({
  id: Joi.string().default(() => generateRandomRequestId()),
  state: Joi.string().default(REQUEST_STATE.DRAFT),
  operatorId: Joi.string().required(),
  template: Joi.string().required(),
  muted: Joi.boolean().default(false),
  language: Joi.string().default('en').min(2).max(2),
  contact: contact.required(),
  request: requestDetails.required(),
  guest: guestDetails,
  activities: Joi.object({ created: Joi.string(), updated: Joi.string() })
    .forbidden()
    .default(() => ({ created: Date.now().toString(), updated: Date.now().toString() })),
  options: requestOptions,
  offers: Joi.array().items(offer).min(1).max(20),
  attributes: Joi.object()
    .pattern(/[\w_]+/, Joi.string().min(1).max(5000))
    .default({}),
});

/**
 *
 * Create Request in Draft state
 *
 * @param {CreateRequestEvent} event
 * @param {any} content
 * @param {function(Error, (CreateRequestResponse|null))} callback
 * @returns {void}
 */
function createRequest(event, content, callback) {
  validateEventSchema(event, eventSchema)
    .then((request) => validateAssociations(request))

    /**
     * TODO: Investigate.. validation fails without changing the official-rate.
     */
    //.then((request) => validateOperatorPermissions(request))
    .then((request) => assignAttributes(request))
    .then((request) => customAttributes.store(request))
    .then((request) => putRequest(request))
    .then((request) => addTags(request))
    .then((request) => scheduleToExpire(request).then(() => request))
    .then((request) => callback(null, { success: true, response: request }))
    .catch((err) => errorHandler(err, callback));
}

/**
 * Validates some request attributes entered by the operator using
 * the operator configured settings (permissions)
 *
 * Permissions Checks:
 *
 *  - Is allowed to customize official-rate.
 *
 * @param {QRequest} request
 * @returns {Promise<QRequest>}
 */
async function validateOperatorPermissions(request) {
  const settings = await getOperatorSettings(request);

  if (settings['preferences'] && settings['preferences']['overwriteOfficialRate'] === false) {
    /**
     * Asserts: official-rates are NOT overwritten.
     * TODO: check if the any rate is changed.
     */

    for (const offer of request.offers) {
      // request availability and compare the official-rates.
      const officialRate = await getOfficialRate(request, offer);

      if (officialRate !== offer.officialRate) {
        throw new ValidationError('official-rate is not permitted to be changed.');
      }
    }
  } else {
    /**
     * All good, no overwrite preference is defined to the operator OR property.
     * bypass to the default behavior (just allow overwrites)
     */
  }

  return request;
}

/**
 *
 * @param {QRequest} request
 * @param {QOffer} offer
 *
 * @returns {Number} officialRate of the offer
 */
async function getOfficialRate(request, offer) {
  const { success, error, response, errorMessage } = await invokeLambda(
    process.env.GET_AVAILABILITY_FUNCTION_NAME,
    {
      accountName: request.request.accountName,
      propertyCode: offer.propertyCode,
      country: request.request.location,
      checkin: offer.checkin,
      nights: offer.nights,
      adults: offer.adults,
      children: offer.children,
      rooms: offer.rooms,
      infants: offer.infants,
      accommodationCode: offer.accommodationCodes[0],
      rate: offer.rateId,
      board: offer.boardId,
    }
  );

  if (response && response.length) {
    return response[0].officialRate;
  } else {
    return 0;
  }
}

/**
 * @param {QRequest} request
 * @returns {Promise<QRequest>}
 */
function assignAttributes(request) {
  return getOperatorSettings(request).then((settings) => {
    request = assignFlowType(request, settings);
    request = resetDates(request, settings);
    return request;
  });
}

/**
 * @param {QPrimitiveRequest} request
 * @returns {Promise<QPrimitiveRequest>}
 */
function validateAssociations(request) {
  const gets = [];
  // validate offers
  request.offers.forEach((offer) => {
    // validate accommodationCodes, includes property-code validation
    offer.accommodationCodes.forEach((roomCode) => {
      // accommodation code validation
      gets.push(
        properties.getAccommodation({
          accountName: request.request.accountName,
          language: request.language,
          propertyCode: offer.propertyCode,
          roomCode,
        })
      );
      // rate validation
      gets.push(
        properties.getRate({
          accountName: request.request.accountName,
          language: request.language,
          propertyCode: offer.propertyCode,
          roomCode,
          rateId: offer.rateId,
        })
      );

      // service code and service defs compatible
      const serviceCodes =
        offer.serviceDefs && offer.serviceDefs.defs && offer.serviceDefs.defs.length
          ? offer.serviceDefs.defs.map((def) => def.code)
          : offer.serviceCodes || [];

      if (serviceCodes.length) {
        serviceCodes.forEach((serviceCode) => {
          gets.push(
            properties.getService({
              accountName: request.request.accountName,
              language: request.language,
              propertyCode: offer.propertyCode,
              roomCode,
              rateId: offer.rateId,
              serviceCode,
            })
          );
        });
      }
    });
  });

  // validate request.operator, includes account validation
  gets.push(
    getOperator({
      accountName: request.request.accountName,
      language: request.language,
      operatorId: request.operatorId,
    })
  );

  // validate request.property, includes account validation
  gets.push(
    properties.getProperty({
      accountName: request.request.accountName,
      language: request.language,
      propertyCode: request.request.propertyCode,
    })
  );

  // when associated data are not available, this promise will rejected.
  return Promise.all(gets)
    .then(() => customAttributes.validate(request))
    .then(() => request)
    .catch((err) => {
      throw new ValidationError(err.message);
    });
}

module.exports = { handler: middlewares.init(createRequest), eventSchema };
