'use strict';
const Bluebird = require('bluebird');
const { NoFullAvailabilityError, ValidationError } = require('./../errors');

const logger = require('@quotelier/logger');
const {
  scheduleToMarkToFollowUp,
  emptySchedulerForRequest,
  scheduleToExpire,
  scheduleToPurgeRequest,
  scheduleToNotifyExpiring,
} = require('../lib/scheduler');
const { invokeLambda } = require('@quotelier/invokeLambda');
const sendEmail = require('./../lib/sendEmail');
const { getEarliestDate } = require('./../lib/getEarliestDate');
const { moveDateToHour } = require('./../lib/getExpireAtDate');
const { getOperatorSettings } = require('./../lib/operator');
const errorHandler = require('./errorHandler');
const { createReservationPayload } = require('./sendRequest');
const requestService = require('./../lib/request');
const populateRequest = require('./../lib/populateRequest');
const url = require('url');
const qs = require('querystring');
const Promise = require('bluebird');
const middlewares = require('../middlewares');

const {
  GET_RESERVATION_FUNCTION_NAME,
  CREATE_RESERVATION_FUNCTION_NAME,
  CANCEL_RESERVATION_FUNCTION_NAME,
  REQUESTS_TABLE,
} = process.env;

const AWS = require('aws-sdk');
const dynamoDbClient = new AWS.DynamoDB.DocumentClient();

/**
 * @typedef {object} Response
 * @param {{state: {lock:boolean}}} proposal
 */

/**
 * Handle the Accept command with Option
 *
 * @param {object} event
 * @param {string} event.requestId
 * @param {string[]|string} event.optionIds List of strings or comma separated option-ids
 *
 * @param {Object} context
 * @param {Function<Response>} callback
 * @returns {void}
 */
function handler(event, context, callback) {
  let { requestId, optionIds } = event;

  logger.log('handler', 'Accept offers with Options', { requestId, optionIds });

  if (typeof optionIds === 'string') {
    // transform comma separated string to array
    optionIds = optionIds.split(',').map((id) => id.trim());
  }
  Promise.try(() => {
    if (!requestId) {
      throw new ValidationError('requestId is required');
    }
    if (optionIds === undefined) {
      throw new ValidationError('optionIds is required');
    }
  })
    .then(() => asyncHandler(requestId, optionIds))
    .then((response) => callback(null, { success: true, response }))
    .catch((err) => errorHandler(err, callback));
}

/**
 * A more clear rewrite of logic using async for readability.
 *
 * @param {string} requestId
 * @param {string[]} optionIds
 * @returns {any}
 */
async function asyncHandler(requestId, optionIds) {
  const request = await requestService.getRequestById(requestId);

  const locked = await requestService.lock.lock(requestId, 'accept');

  if (locked === false) {
    // was already locked.
    throw new ValidationError('request action is locked, please wait.');
  }

  try {
  isApplicableToAccept(request, optionIds);
  const offers = getOffersInOptions(request, optionIds);
    const response = await executeFlow(request, offers, optionIds);
    await requestService.lock.unlock(requestId, 'accept');
    return response;
  } catch (err) {
    /**
     * Unlock
     */
    await requestService.lock.unlock(requestId, 'accept');
    throw err;
  }
}

/**
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} offers
 * @param {string} optionIds
 * @returns {Promise<any>}
 */
async function executeFlow(request, offers, optionIds) {
  let processedRequest;

  if (request.options.flowType === 'selectOne') {
    processedRequest = await handleSelectOne(request, offers, optionIds);
  } else {
    processedRequest = await handleSelectMany(request, offers, optionIds);
  }

  return {
    state: processedRequest.state,
    options: getOptionStatuses(request, processedRequest.offers),
    offers: processedRequest.offers.map((offer, index) => {
      return {
        accepted: offer.accepted,
        paymentUrl: offer.paymentUrl ?? null,
        index: index,
      };
    }),
    proposal: {
      state: {
        lock: false,
      },
    },
  };
}

/**
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} offers
 *
 * @returns {Array<{accepted: boolean, id: string}>}
 */
function getOptionStatuses(request, offers) {
  return (
    offers
      // only offers with optionIds
      .filter((offer) => offer.optionIds && offer.optionIds.length)

      // collect all options from offers
      .reduce((options, offer) => options.concat(offer.optionIds), [])

      // remove doublicates
      .filter((option, index, self) => self.indexOf(option) === index)

      // map into a status object
      .map((option) => ({
        id: option,
        accepted: (request.acceptedOptionIds || []).indexOf(option) !== -1,
      }))
  );
}

/**
 * @param {QPrimitiveRequest} request
 * @param {string} optionIds
 * @returns {QPrimitiveRequest}
 */
function isApplicableToAccept(request, optionIds) {
  const offers = getOffersInOptions(request, optionIds);
  if (!requestService.canMoveStateTo(request.state, 'accepted')) {
    throw new Error(`Can not move state "${request.state}" to state "accepted"`);
  } else if (!offers.length) {
    throw new ValidationError(`There is no offer associated with the option "${optionIds}"`);
  }

  if (request.options.flowType === 'selectOne') {
    if (request.state === 'accepted') {
      throw new ValidationError(`Multiple group selections are not allowed`);
    }
    if (!offers.every((offer) => !offer.accepted)) {
      throw new ValidationError(`Option "${optionIds}" contains already accepted offers`);
    }
    if (optionIds.length > 1) {
      throw new ValidationError(`Multiple option selections not allowed on this request`);
    }
  }

  return request;
}

/**
 * @param {QPrimitiveRequest} request
 * @param {string[]} optionIds

 * @returns {QPrimitiveOffer[]}
 */
function getOffersInOptions(request, optionIds) {
  return optionIds
    .map((optionId) => {
      return request.offers.filter((offer) => (offer.optionIds || []).indexOf(optionId) !== -1);
    })
    .reduce((acceptedOffers, offers) => {
      offers.forEach((offer) => {
        // avoid duplicates
        if (acceptedOffers.indexOf(offer) === -1) {
          acceptedOffers.push(offer);
        }
      });
      return acceptedOffers;
    }, []);
}

/**
 * @param {QPrimitiveRequest} request
 * @param {string[]} optionIds

 * @returns {QPrimitiveOffer[]}
 */
function getOffersNotInOptions(request, optionIds) {
  const offersInOptions = getOffersInOptions(request, optionIds);
  return request.offers.filter((offer) => {
    return offersInOptions.indexOf(offer) === -1;
  });
}

/**
 *
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} offers
 * @param {String} optionIds
 * @returns {Promise<{request: QPrimitiveRequest, reservedOffers: QPrimitiveOffer[]}>}
 */
async function handleSelectMany(request, offers, optionIds) {
  logger.log('handleSelectMany', 'Checking for full availability');

  const targettedOffers = getOffersInOptions(request, optionIds);

  const targettedOffersWithoutReleaseAt = targettedOffers
    .filter((offer) => !offer.accepted)
    .filter((offer) => !offer.releaseAt);

  if (targettedOffersWithoutReleaseAt.length) {
    const availabilities = await Bluebird.map(targettedOffersWithoutReleaseAt, (offer) =>
      invokeLambda(process.env.GET_AVAILABILITY_FUNCTION_NAME, {
        accountName: request.request.accountName,
        propertyCode: offer.propertyCode,
        country: request.request.location,
        checkin: offer.checkin,
        nights: offer.nights,
        adults: offer.adults,
        children: offer.children,
        rooms: offer.rooms,
        infants: offer.infants,
        accommodationCode: offer.accommodationCodes[0],
        rate: offer.rateId,
        board: offer.boardId,
      }).then((invocationResult) => {
        invocationResult.response = (invocationResult.response || []).filter((availabilityOffer) => {
          return availabilityOffer.remaining > 0;
        });
        return invocationResult;
      })
    );


    const availabilityResult = availabilities.every((availability) => {
      const { success, error, response, errorMessage } = availability;

      if (success && !response.length) {
        return false;
      } else if (!success) {
        return new Error(errorMessage || error.message);
      } else {
        return true;
      }
    });

    if (availabilityResult === false) {
      return handleNoFullAvailabilityCase(request, optionIds);
    } else if (availabilityResult !== true) {
      logger.error('handleSelectMany', 'Unhandled get-availability response error', availabilityResult);
      return handleNoFullAvailabilityCase(request, optionIds);
    } else {
      return acceptRequest(request, offers, optionIds);
    }
  } else {
    /**
     * Just accept, all offers
     */
    return acceptRequest(request, offers, optionIds);
  }

}

/**
 *
 * The purpose of this is to check for full availability of the
 * selected option and either continue with the Acceptance of the Request,
 * either move it to unavailable state.
 *
 * 1) Check if current selected optionId has releaseAt set
 * 2) Cancel all offers that are not in the selected optionId
 * 3) Check availability for every offer inside the selected optionId
 * 4) If there is no full availability, put request in unavailable state
 * 5) Else continue with the flow
 *
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} offers
 * @param {string[]} optionIds
 * @returns {Promise<{request: QPrimitiveRequest, reservedOffers: QPrimitiveOffer[]}>}
 */
async function handleSelectOne(request, offers, optionIds) {
  logger.log('handleSelectOne', 'Checking for full availability');

  /**
   * At least one offer has release-at.
   */
  const hasReleaseAt = offers.every((offer) => !offer.releaseAt) === false; // no offer has release at

  const offersNotInOption = getOffersNotInOptions(request, optionIds);

  if (hasReleaseAt) {
    logger.log('handleSelectOne', 'Offers in group have releaseAt. Request accepted.');

    if (offersNotInOption.length) {
      await rollbackReservedOffers(request, offersNotInOption);
    }

    return acceptRequest(request, offers, optionIds);
  } else {
    logger.log('handleSelectOne', 'Offers in group have no releaseAt');

    await rollbackReservedOffers(request, offersNotInOption);

    /**
     * Request availability for all offers
     */
    const availabilities = await Bluebird.map(offers, (offer) =>
      invokeLambda(process.env.GET_AVAILABILITY_FUNCTION_NAME, {
        accountName: request.request.accountName,
        propertyCode: offer.propertyCode,
        country: request.request.location,
        checkin: offer.checkin,
        nights: offer.nights,
        adults: offer.adults,
        children: offer.children,
        rooms: offer.rooms,
        infants: offer.infants,
        accommodationCode: offer.accommodationCodes[0],
        rate: offer.rateId,
        board: offer.boardId,
      }).then((invocationResult) => {
        invocationResult.response = (invocationResult.response || []).filter(
          (availabilityOffer) => {
            return availabilityOffer.remaining > 0;
          }
        );
        return invocationResult;
      })
    );

    const availabilityResult = availabilities.every((availability) => {
      const { success, error, response, errorMessage } = availability;

      if (success && !response.length) {
        return false;
      } else if (!success) {
        return new Error(errorMessage || error.message);
      } else {
        return true;
      }
    });

    if (availabilityResult === false) {
      return handleNoFullAvailabilityCase(request, optionIds);
    } else if (availabilityResult !== true) {
      // result is an error
      logger.error('handleSelectOne', 'Unhandled get-availability response error', availabilityResult);
      return handleNoFullAvailabilityCase(request, optionIds);
    } else {
      return acceptRequest(request, offers, optionIds);
    }
  }
}

/**
 *
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveRequest[]} offers
 * @param {string[]} optionIds
 * @returns {Promise<QRequest>}
 */
async function acceptRequest(request, offers, optionIds) {
  logger.log('acceptRequest', 'Accept request', { optionIds });

  const populatedRequestWithAttributes = await populateRequest.populateAttributes(request);

  const reservationResponse = await reserveOffers(populatedRequestWithAttributes, offers);
  request = reservationResponse.request;
  const operatorSettings = await getOperatorSettings(request);

  request = await updateRequest(request, reservationResponse.reservedOffers, operatorSettings, optionIds);

  /**
   * UPDATE REQUEST SCHEDULAR ENTRIES.
   */
  await emptySchedulerForRequest(request.id);
  await requestService.markRequestToFollowUp(request.id, false);
  await scheduleToMarkToFollowUp(request, operatorSettings);
  await scheduleToExpire(request);
  await scheduleToPurgeRequest(request.id);
  await scheduleToNotifyExpiring(request, operatorSettings);

  // Purge request only if the template contains the hybrid
  if (request.template.indexOf('hybrid') !== -1) {
    await scheduleToPurgeRequest(request.id);
  }

  const populatedRequest = await populateRequest.populateRequest(request);

  await sendEmail.sendEmailToCustomer(
    populatedRequest,
    'customerPaymentOptions',
    'payment-options',
    'contact',
    getIndexOfFirstAcceptedOffer(populatedRequest, optionIds)
  );

  await sendEmail.sendEmailToOperator(
    populatedRequest,
    'operatorAccepted',
    'proposal-accepted',
    'operator',
    getIndexOfFirstAcceptedOffer(populatedRequest, optionIds)
  );

  return populatedRequest;
}

/**
 * Transform the grouped payment method url to single payment method url.
 *
 * @todo: Encapsulate this transformation to the integration by defining a reservation payment method
 *
 * @param {string} paymentURL
 * @returns {string}
 */
function transformWHPaymentUrl(paymentURL) {
  const { protocol, hostname, query } = url.parse(paymentURL);
  
  if (hostname.includes('reduniq')) {
     return paymentURL;
  } else {
    /**
     * Webhotelier modifies reservation ids to prevent fishing attacks.
    *
    * Parse reservation-id to integer and subtract with 10.000.000.
    */

    const { email, res_id } = qs.parse(query);
    const secureReservationId = parseInt(res_id) - 10000000;

    return url.format({
      protocol,
      hostname,
      pathname: '/',
      query: {
        res_id: secureReservationId.toString(),
        email,
        view: 'cc',
      },
    });
  }
}

/**
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} offers
 * @returns {Promise<{request: QPrimitiveRequest, reservedOffers: QPrimitiveOffer[]}>}
 */
function reserveOffers(request, offers) {
  logger.log('reserveOffers', 'Reserving offers');

  const reservedOffers = [];

  let unprocessedOffers = offers;

  return Bluebird.each(offers, (offer, index) => {
    if (offer.accepted === true) {
      return null;
    }
    return reserveOffer(request, offer).then(() => {
      // move from unprocessed to reserved
      unprocessedOffers.splice(index, 1);
      return reservedOffers.push(offer);
    });
  })
    .then(() => ({ request, reservedOffers }))
    .catch((err) => {
      request.errors = [err.message]; // assign request error
      logger.log('reserveOffers', 'reservation failure, perform rollback', { err });

      return rollbackReservedOffers(request, reservedOffers)
        .then((cancelledOffers) => unprocessedOffers.concat(cancelledOffers))
        .then((reservedOffers) => ({ request, reservedOffers }));
    });
}

/**
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} reservedOffers
 * @returns {Promise<{request: QPrimitiveRequest, reservedOffers: QPrimitiveOffer[]}>}
 */
function rollbackReservedOffers(request, reservedOffers) {
  logger.log('rollbackReservedOffers', 'Rolling back reserved offers');

  return Bluebird.map(reservedOffers, (offer) => {
    offer.paymentUrl = null;
    if (!offer.reservationId) {
      return offer;
    }
    return invokeLambda(CANCEL_RESERVATION_FUNCTION_NAME, {
      accountName: request.request.accountName,
      propertyCode: offer.propertyCode,
      reservationId: offer.reservationId,
    }).then(() => {
      // unset reservation details
      offer.reservationId = null;
      return offer;
    });
  });
}

/**
 *
 * This is a hack to get the first offer
 * of the accepted option and pass it to
 * old templates that expect an offer
 *
 * @param {QPrimitiveRequest} request
 * @param {string[]} optionIds
 * @returns {Number}
 */
function getIndexOfFirstAcceptedOffer(request, optionIds) {
  const acceptedOffers = getOffersInOptions(request, optionIds);
  return request.offers.indexOf(acceptedOffers[0]);
}

/**
 * @private
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer} offer
 * @returns {Promise<QPrimitiveOffer>}
 */
function reserveOffer(request, offer) {
  logger.log('reserveOffer', 'Reserving offer', offer);

  return Bluebird.try(() =>
    checkIfOfferHasActiveReservation(
      request.request.accountName,
      offer.reservationId,
      offer.propertyCode
    )
  )
    .then((hasActiveReservation) => {
      if (hasActiveReservation) {
        logger.log('acceptOffer', 'Reservation is active', {
          offer: offer,
          requestId: request.id,
        });
        return offer;
      } else {
        const payload = createReservationPayload(request, offer);
        const offerIndex = request.offers.indexOf(offer);
        return invokeLambda(CREATE_RESERVATION_FUNCTION_NAME, payload).then(
          ({ success, response, error, errorMessage }) => {
            if (success) {
              offer.reservationId = response.reservationId;
              offer.paymentUrl = response.paymentUrl;
              return offer;
            } else if (!success && error) {
              throw new Error(`offerId(${offerIndex}) ${error.message}`);
            } else if (errorMessage) {
              throw new Error(`offerId(${offerIndex}) ${errorMessage}`);
            } else {
              throw new Error(
                `offerId(${offerIndex}) ` + 'Unexpected integration response',
                JSON.stringify({ success, response, error, errorMessage })
              );
            }
          }
        );
      }
    })
    .then((offer) => {
      /**
       * transform webhotelier payment url.
       */
      const { flowType } = request.options || {};
      if (flowType === 'selectMany') {
        if (offer.paymentUrl) {
          offer.paymentUrl = transformWHPaymentUrl(offer.paymentUrl);
        }
        return offer;
      } else {
        return offer;
      }
    });
}

/**
 * @private
 * @param {Object} accountName
 * @param {string} reservationId
 * @param {string} propertyCode
 * @returns {(Promise<Boolean>|Boolean)}
 */
function checkIfOfferHasActiveReservation(accountName, reservationId, propertyCode) {
  logger.log(
    'checkIfOfferHasActiveReservation',
    'Checking offer for active reservation',
    reservationId
  );

  if (reservationId) {
    return invokeLambda(GET_RESERVATION_FUNCTION_NAME, {
      accountName,
      propertyCode,
      reservationId,
    }).then((response) => {
      if (response.success) {
        return response.response.status === 'active';
      } else {
        throw new Error(
          'Get reservation invocation was not successful ' + JSON.stringify(response.error)
        );
      }
    });
  } else {
    return Promise.resolve(false);
  }
}

/**
 * @param {QPrimitiveRequest} request
 * @param {QPrimitiveOffer[]} offers
 * @param {QSettings} settings
 * @param {string[]} optionIds
 * @returns {Promise<QPrimitiveRequest>}
 */
function updateRequest(request, offers, settings, optionIds) {
  logger.log('updateRequest', 'updates', { request, offers, settings });

  request.acceptedOptionIds = request.acceptedOptionIds || [];
  request.acceptedOptionIds = request.acceptedOptionIds.concat(optionIds);

  const updateAttributeValues = {
    ':dateNow': Date.now().toString(),
    ':accepted': true,
    ':acceptedOptionIds': request.acceptedOptionIds,
    ':state': 'accepted',
  };

  const updateAttributeNames = {
    '#state': 'state',
    '#updated': 'updated',
  };

  const updateExpressions = [
    'activities.accepted = :dateNow',
    'activities.#updated = :dateNow',
    'acceptedOptionIds = :acceptedOptionIds',
    '#state = :state',
    'updatedAt = :dateNow',
  ];

  const checkinDate = getEarliestDate(request.offers.map((offer) => offer.checkin));

  updateAttributeNames['#request'] = 'request';
  updateAttributeValues[':expireAtDate'] = moveDateToHour(checkinDate, settings.hours.expire);
  updateExpressions.push('#request.expireAt = :expireAtDate');

  if (request.errors && request.errors.length) {
    updateAttributeValues[':errors'] = request.errors;
    updateExpressions.push(`errors = :errors`);
    updateExpressions.push(`activities.failed = :dateNow`);
  }

  /**
   * Generate dynamically update expression based on reserved offers.
   */
  offers.forEach((offer) => {
    const offerIndex = request.offers.indexOf(offer);
    if (offerIndex === -1) {
      throw new Error('Inconsistent request offer mapping:' + JSON.stringify({ request, offer }));
    }
    updateExpressions.push(`offers[${offerIndex}].accepted = :accepted`);
    if (offer.paymentUrl && offer.reservationId) {
      updateExpressions.push(
        `offers[${offerIndex}].reservationId = :reservationIdOffer${offerIndex}`
      );
      updateExpressions.push(`offers[${offerIndex}].paymentUrl = :paymentUrlOffer${offerIndex}`);
      updateAttributeValues[`:paymentUrlOffer${offerIndex}`] = offer.paymentUrl;
      updateAttributeValues[`:reservationIdOffer${offerIndex}`] = offer.reservationId;
    } else if (!offer.paymentUrl && offer.reservationId) {
      /**
       * Update / Set reservation ID even without payment-URL
       */
      updateExpressions.push(
        `offers[${offerIndex}].reservationId = :reservationIdOffer${offerIndex}`
      );
      updateAttributeValues[`:reservationIdOffer${offerIndex}`] = offer.reservationId;
    }
  });
  return dynamoDbClient
    .update({
      TableName: REQUESTS_TABLE,
      Key: { id: request.id },
      UpdateExpression: 'Set ' + updateExpressions.join(', '),
      ReturnValues: 'ALL_NEW',
      ExpressionAttributeValues: updateAttributeValues,
      ExpressionAttributeNames: updateAttributeNames,
    })
    .promise()
    .then((response) => response.Attributes);
}

/**
 * @param {QPrimitiveRequest} request
 * @param {string[]} optionIds
 * @returns {Promise<QPrimitiveRequest>}
 */
function handleNoFullAvailabilityCase(request, optionIds) {
  logger.log('handleNoFullAvailabilityCase', 'Handle no full availability');

  return putRequestInUnavailableState(request, optionIds)
    .then(() => emptySchedulerForRequest(request.id))
    .then(() => requestService.markRequestToFollowUp(request.id, false))
    .then(() =>
      sendEmail.sendEmailToOperator(
        request.id,
        'operatorUnavailable',
        'proposal-unavailable',
        'operator'
      )
    )
    .then(() => request);
}

/**
 * @param {QPrimitiveRequest} request
 * @param {string[]} optionIds
 * @returns {Promise<QPrimitiveRequest>}
 */
function putRequestInUnavailableState(request, optionIds) {
  logger.log('putRequestInUnavailableState', 'Updating', request.id);

  request.acceptedOptionIds = request.acceptedOptionIds || [];
  request.acceptedOptionIds = request.acceptedOptionIds.concat(optionIds);

  const updateAttributeValues = {
    ':dateNow': Date.now().toString(),
    ':acceptedOptionIds': request.acceptedOptionIds,
    ':state': 'unavailable',
    ':accepted': true,
  };

  const updateAttributeNames = {
    '#state': 'state',
    '#updated': 'updated',
    '#unavailable': 'unavailable',
    '#accepted': 'accepted',
  };

  const updateExpressions = [
    'activities.#unavailable = :dateNow',
    'activities.#updated = :dateNow',
    'activities.#accepted = :dateNow',
    'acceptedOptionIds = :acceptedOptionIds',
    '#state = :state',
    'updatedAt = :dateNow',
  ];

  /**
   * Generate dynamically update expression based on reserved offers.
   */
  const acceptedOffers = getOffersInOptions(request, optionIds);

  acceptedOffers.forEach((offer) => {
    const offerIndex = request.offers.indexOf(offer);
    updateExpressions.push(`offers[${offerIndex}].#accepted = :accepted`);
  });

  return dynamoDbClient
    .update({
      TableName: REQUESTS_TABLE,
      Key: { id: request.id },
      UpdateExpression: 'Set ' + updateExpressions.join(', '),
      ReturnValues: 'ALL_NEW',
      ExpressionAttributeValues: updateAttributeValues,
      ExpressionAttributeNames: updateAttributeNames,
    })
    .promise()
    .then((response) => response.Attributes);
}

module.exports = {
  handler: middlewares.init(handler),
  isApplicableToAccept,
  checkIfOfferHasActiveReservation,
  getOffersInOptions,
  updateRequest,
  reserveOffers,
  transformWHPaymentUrl,
};
