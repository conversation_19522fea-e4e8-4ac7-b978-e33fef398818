const moment = require('moment');
const {
  guestDetails,
  validateEventSchema,
  contact,
  requestDetails,
  joi,
  channel,
  requestOptions,
} = require('../lib/schemas');
const errorHandler = require('./errorHandler');
const logger = require('@quotelier/logger');
const generateRandomRequestId = require('../lib/generateRandomRequestId');
const { putRequest, REQUEST_STATE } = require('../lib/request');
const { ValidationError } = require('../errors');
const { scheduleToExpire, scheduleToSendRequest } = require('../lib/scheduler');
const assignFlowType = require('../lib/assignFlowType');
const properties = require('../lib/properties');
const sendEmail = require('./../lib/sendEmail');
const { getOperatorSettings } = require('../lib/operator');
const middlewares = require('../middlewares');
const { invokeServiceAsync, invokeService } = require('@quotelier/invokeLambda');
const uuid = require('uuid');

/**
 * @typedef {object} CreateNewRequestEvent
 */

/**
 * @typedef {!LambdaResponse.<any>} CreateNewRequestResponse
 */

const eventSchema = joi.object({
  template: joi.string().max(255),
  muted: joi.boolean().default(false),
  language: joi
    .string()
    .max(255)
    .default('en'),
  contact: contact.required(),
  guest: guestDetails,
  request: requestDetails
    .keys({
      checkin: joi
        .date()
        .raw()
        .min(
          moment()
            .startOf('day')
            .toISOString()
        )
        .format('YYYY-MM-DD')
        .required(),
      channel: channel.default('partners'),
    })
    .required(),
  activities: joi
    .object({ created: joi.string(), updated: joi.string() })
    .forbidden()
    .default(
      () => ({ created: Date.now().toString(), updated: Date.now().toString() })
    ),
  options: requestOptions.forbidden(),
  tags: joi
    .array()
    .items(joi.string())
    .max(20)
    .default([]),
});

/**
 * @param {CreateNewRequestEvent} event
 * @param {any} content
 * @param {function(Error, (CreateNewRequestResponse|null))} callback
 * @returns {void}
 */
function createNewRequest(event, content, callback) {
  /**
   * @type {QRequest}
   */
  let request;

  /**
   * @type {QProperty}
   */
  let property;

  validateEventSchema(event, eventSchema)
    .then(response => (request = response))
    .then(() => validateAssociations(request).then(response => (property = response)))
    .then(() => assignAttributes(request, property).then(response => (request = response)))
    .then(request => putRequest(request))
    .then(request => scheduleToExpire(request).then(() => request))
    .then(request => handleAutomodeSendRequest(request, property))
    .then(() => notifyOperator(request, property))
    .then(() => callback(null, { success: true, response: request }))
    .catch(err => errorHandler(err, callback));
}

/**
 *
 * @param {QRequest} request
 * @param {QProperty} property
 * @returns {Promise<any>}
 */
function notifyOperator(request, property) {
  const isAutoMode = property && property.automode && property.automode.enabled;

  if (property.contact && property.contact.email) {
    return sendEmail.sendEmail(
      property.contact.email,
      request,
      isAutoMode ? 'operatorAutoMode' : 'operatorNew',
      'new-request',
      'operator'
    );
  } else {
    logger.log(
      'notifyOperator',
      'Could not notify operator, property does not provide any notification target',
      { request, property }
    );
  }
}

/**
 * @param {QRequest} request
 * @param {QProperty} property
 * @returns {Promise<any>}
 */
function handleAutomodeSendRequest(request, property) {
  const isAutoMode = property && property.automode && property.automode.enabled;
  if (isAutoMode) {
    return scheduleToSendRequest(request.id, property.automode.delayMinutes || 10);
  }
  return Promise.resolve();
}

/**
 *
 * @param {QRequest} request
 * @returns {Promise<QProperty>}
 */
function validateAssociations(request) {
  return properties
    .getProperty({
      accountName: request.request.accountName,
      language: request.language,
      propertyCode: request.request.propertyCode,
    })
    .catch(() => {
      throw new ValidationError(`Property reference "${request.propertyCode}" not available.`);
    });
}

/**
 * @param {QRequest} request
 * @param {QProperty} property
 * @returns {Promise<QRequest>}
 */
async function assignAttributes(request, property) {
  const isAutoMode = property && property.automode && property.automode.enabled;
  const newRequest = isAutoMode ? await autoModeAttributes(request, property) : newRequestAttributes(request);
  return getOperatorSettings(newRequest).then(settings => assignFlowType(newRequest, settings));
}

/**
 * @param {QRequest} request
 * @returns {Promise<QRequest>}
 */
function newRequestAttributes(request) {
  request.id = generateRandomRequestId();
  request.state = REQUEST_STATE.NEW;
  request.offers = [];
  request.template = 'proposal';
  request.request.expireAt = moment(request.request.checkin).format();

  return request
}

/**
 * @param {QRequest} request
 * @param {QProperty} property
 * @returns {Promise<QRequest>}
 */
async function autoModeAttributes(request, property) {
  request.id = generateRandomRequestId();
  request.state = REQUEST_STATE.DRAFT;
  request.offers = await getAvailabilityOffers(request, property);
  request.template = property.automode.template;
  request.operatorId = property.automode.operator;
  request.request.expireAt = moment(request.request.checkin).format();

  return request
}

/**
 * @param {QRequest} request
 * @param {QProperty} property
 * @returns {Promise<QAvailabilityOffer[]>}
 */
async function getAvailabilityOffers(request, property) {
  const prefix = property.automode.prefix;
  const bookingCode = property.automode.bookingCode;

  const { accountName, propertyCode, checkin, nights, adults, children, infants, rooms, accommodationCode, rate, board, country } = request.request;
  const availabilityOffers = await invokeService('integrations', 'getAvailability', {
    accountName,
    propertyCode,
    checkin,
    nights,
    adults,
    children,
    infants,
    rooms,
    accommodationCode,
    rate,
    board,
    country,
    bookingCode,
  }).then(res => res.response);

  // Get required services for each offer and add them to the offer
  const serviceCodes = await Promise.all(
    availabilityOffers.map(availability =>
      invokeServiceAsync('integrations', 'getServicesAvailability', {
        accountName,
        propertyCode,
        checkin,
        nights,
        adults,
        children,
        infants,
        rooms,
        rateId: availability.rateId,
        accommodationCode: availability.accommodationCode,
        country,
        bookingCode,
      }).then(res => res.response)
    )
  );

  // Filter services that are not required
  const requiredServices = serviceCodes.map(offerServices => offerServices.filter(service => service.required));

  // Transform Response to Offer
  const offers = availabilityOffers.map((availability, index) => ({
    checkin,
    nights,
    rooms,
    children,
    adults,
    infants,
    propertyCode,
    optionIds: [uuid.v1()],
    accepted: false,
    title: availability.room,
    description: availability.rate,
    rateId: availability.rateId,
    currency: availability.currency,
    accommodationCodes: [availability.accommodationCode],
    serviceCodes: serviceCodes[index],
    officialRate: availability.officialRate,
    roomRate: availability.roomRate,
  }));

  return offers.filter(offer => offer.description.includes(prefix))
};

module.exports = { handler: middlewares.init(createNewRequest) };
