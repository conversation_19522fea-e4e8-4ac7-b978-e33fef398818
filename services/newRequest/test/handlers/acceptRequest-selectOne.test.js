const expect = require('chai').expect;
const nock = require('nock');
const sinon = require('sinon');
const { ValidationError } = require('../../errors');
const {
  isApplicableToAccept,
  checkIfOfferHasActiveReservation,
  handler,
} = require('./../../handlers/acceptRequest');
const { getRequestById } = require('./../../lib/request');
const populateRequest = require('../../lib/populateRequest');
const sendEmail = require('./../../lib/sendEmail');
const mock = require('./../mock');
const fixtures = require('./../fixtures');

const { AWS_ENDPOINT } = process.env;

describe('acceptRequest (selectOne)', () => {
  let requestFixture;

  mock.requestLock.initTable();

  beforeEach(() => {
    requestFixture = JSON.parse(JSON.stringify(require('./../fixtures/request.json')));
  });

  describe('Validation', () => {
    mock.request.mockTableWithData([
      (() => {
        let record = Object.assign(
          {},
          fixtures.get('request/valid-update-existing-request-record.json'),
          {
            state: 'opened',
          }
        );
        record.offers = record.offers.map((offer) => {
          offer.optionIds = ['test'];
          return offer;
        });
        return record;
      })(),
    ]);

    it('should require requestId parameter', (done) => {
      handler({}, null, (err, res) => {
        expect(err).to.be.null;
        expect(res.success).to.be.false;
        expect(res.response).to.be.null;
        expect(res.error.type).to.eq('ValidationError');
        expect(res.error.message).to.eq('requestId is required');
        done();
      });
    });

    it('should require offerId parameter', (done) => {
      handler({ requestId: 'foo' }, null, (err, res) => {
        expect(err).to.be.null;
        expect(res.success).to.be.false;
        expect(res.response).to.be.null;
        expect(res.error.type).to.eq('ValidationError');
        expect(res.error.message).to.eq('optionIds is required');
        done();
      });
    });

    it('should require Request to exist', (done) => {
      handler(
        {
          requestId: 'NotExistingId',
          optionIds: ['test'],
        },
        null,
        (err, res) => {
          expect(err).to.be.null;
          expect(res.success).to.be.false;
          expect(res.response).to.be.null;
          expect(res.error.type).to.eq('NotFoundError');
          expect(res.error.message).to.eq('Request not found');
          done();
        }
      );
    });
  });

  describe('Accept with releaseAt set', () => {
    mock.request.mockTableWithData([
      (() => {
        let record = Object.assign(
          {},
          fixtures.get('request/valid-update-existing-request-record.json'),
          {
            state: 'opened',
          }
        );
        record.offers = record.offers.map((offer) => {
          offer.optionIds = ['test'];
          offer.releaseAt = offer.checkin;
          return offer;
        });
        return record;
      })(),
    ]);

    it('should accept Request without checking for availability', (done) => {
      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getOperatorSettings/invocations', {
          operatorId: '<EMAIL>',
          accountName: 'DEMO',
          propertyCode: 'demo',
          language: 'en',
        })
        .reply(200, fixtures.get('operator/settings.json'));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/quotelier-attribute-store-test-get/invocations', {
          accountName: 'DEMO',
          propertyCode: 'demo',
          id: 'request-foo',
        })
        .delay(50)
        .reply(200, JSON.stringify({operatorCodeName: "QKK", channelCode: "HOTEL CONTACT"}));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/removeEventFromScheduler/invocations', {
          requestId: 'foo',
          action: '*',
        })
        .reply(200, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: '2017-08-16T17:00:00+03:00',
          action: 'expire',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: /20[0-9]{2}\-[0-9]{2}\-[0-9]{2}T\d{2}\:\d{2}\:00\+\d{2}\:00/, // eslint-disable-line
          action: 'purgeRequest',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: '2017-08-15T17:00:00+03:00',
          action: 'notifyExpiring',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: '2017-08-15T12:00:00+03:00',
          action: 'markToFollowUpBeforeRelease',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations')
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/createReservation/invocations')
        .reply(
          200,
          JSON.stringify({
            success: true,
            response: {
              paymentUrl: 'http://localhost:8000',
              reservationId: 'RES_ID_XXX',
            },
          })
        );
      sinon.stub(sendEmail, 'sendEmailToCustomer').resolves();
      sinon.stub(sendEmail, 'sendEmailToOperator').resolves();
      requestFixture.offers[0].optionIds = ['test'];
      requestFixture.offers[0].accepted = false;
      sinon.stub(populateRequest, 'populateRequest').resolves(requestFixture);

      handler(
        {
          requestId: 'foo',
          optionIds: ['test'],
        },
        null,
        (err, res) => {
          if (err) {
            console.error(err);
          }

          expect(err).to.be.null;
          expect(res.success).to.be.true;

          sendEmail.sendEmailToCustomer.restore();
          sendEmail.sendEmailToOperator.restore();
          populateRequest.populateRequest.restore();

          getRequestById('foo')
            .then((request) => {
              expect(request.state).to.eq('accepted');
              expect(parseInt(request.activities.accepted)).to.lessThan(Date.now());
              expect(request.request.expireAt).to.eq('2017-08-16T17:00:00+03:00');
              expect(request.offers[0].accepted).to.be.true;
              done();
              return;
            })
            .catch((err) => done(err));
        }
      );
    });
  });

  describe('Accept without releaseAt set', () => {
    mock.request.mockTableWithData([
      (() => {
        let record = Object.assign(
          {},
          fixtures.get('request/valid-update-existing-request-record.json'),
          {
            state: 'opened',
          }
        );
        record.offers = record.offers.map((offer) => {
          offer.optionIds = ['test'];
          offer.releaseAt = undefined;
          return offer;
        });
        return record;
      })(),
    ]);

    it('should accept Request with checking and having full availability', (done) => {
      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getOperatorSettings/invocations', {
          operatorId: '<EMAIL>',
          accountName: 'DEMO',
          propertyCode: 'demo',
          language: 'en',
        })
        .reply(200, fixtures.get('operator/settings.json'));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/quotelier-attribute-store-test-get/invocations', {
          accountName: 'DEMO',
          propertyCode: 'demo',
          id: 'request-foo',
        })
        .delay(50)
        .reply(200, JSON.stringify({operatorCodeName: "QKK", channelCode: "HOTEL CONTACT"}));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/removeEventFromScheduler/invocations', {
          requestId: 'foo',
          action: '*',
        })
        .reply(200, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: '2017-08-16T17:00:00+03:00',
          action: 'expire',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: /20[0-9]{2}\-[0-9]{2}\-[0-9]{2}T\d{2}\:\d{2}\:00\+\d{2}\:00/, // eslint-disable-line
          action: 'purgeRequest',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: '2017-08-15T17:00:00+03:00',
          action: 'notifyExpiring',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations', {
          requestId: 'foo',
          date: '2017-08-15T12:00:00+03:00',
          action: 'markToFollowUpBeforeRelease',
        })
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/addEventToScheduler/invocations')
        .reply(202, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getAvailability/invocations')
        .reply(200, JSON.stringify({ success: true, response: [{ remaining: 5 }] }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/createReservation/invocations')
        .reply(
          200,
          JSON.stringify({
            success: true,
            response: {
              paymentUrl: 'http://localhost:8000',
              reservationId: 'RES_ID_XXX',
            },
          })
        );
      sinon.stub(sendEmail, 'sendEmailToCustomer').resolves();
      sinon.stub(sendEmail, 'sendEmailToOperator').resolves();
      requestFixture.offers[0].optionIds = ['test'];
      requestFixture.offers[0].accepted = false;
      sinon.stub(populateRequest, 'populateRequest').resolves(requestFixture);
      handler(
        {
          requestId: 'foo',
          optionIds: ['test'],
        },
        null,
        (err, res) => {
          expect(err).to.be.null;
          expect(res.success).to.be.true;

          sendEmail.sendEmailToCustomer.restore();
          sendEmail.sendEmailToOperator.restore();
          populateRequest.populateRequest.restore();

          getRequestById('foo')
            .then((request) => {
              expect(request.state).to.eq('accepted');
              expect(parseInt(request.activities.accepted)).to.lessThan(Date.now());
              expect(request.request.expireAt).to.eq('2017-08-16T17:00:00+03:00');
              expect(request.offers[0].accepted).to.be.true;
              done();
              return;
            })
            .catch((err) => done(err));
        }
      );
    });

    it('should transition Request to unavailable state with no or some availability', (done) => {
      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/removeEventFromScheduler/invocations', {
          requestId: 'foo',
          action: '*',
        })
        .reply(200, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getAvailability/invocations')
        .reply(
          200,
          JSON.stringify({
            success: true,
            response: [],
          })
        );

      sinon.stub(sendEmail, 'sendEmailToOperator').resolves();

      handler(
        {
          requestId: 'foo',
          optionIds: ['test'],
        },
        null,
        (err, res) => {
          if (err) {
            return done(err);
          }
          expect(res.success).to.be.true;

          sendEmail.sendEmailToOperator.restore();

          getRequestById('foo')
            .then((request) => {
              expect(request.state).to.eq('unavailable');
              expect(parseInt(request.activities.unavailable)).to.lessThan(Date.now());
              expect(request.acceptedOptionIds).to.have.length(1);
              done();
              return;
            })
            .catch((err) => done(err));
        }
      );
    });
    it('should transition Request to unavailable state with ZERO remaining availability results', (done) => {
      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/removeEventFromScheduler/invocations', {
          requestId: 'foo',
          action: '*',
        })
        .reply(200, JSON.stringify({ success: true, response: null }));

      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getAvailability/invocations')
        .reply(
          200,
          JSON.stringify({
            success: true,
            response: [{ remaining: 0 }],
          })
        );

      sinon.stub(sendEmail, 'sendEmailToOperator').resolves();

      handler(
        {
          requestId: 'foo',
          optionIds: ['test'],
        },
        null,
        (err, res) => {
          if (err) {
            return done(err);
          }
          expect(res.success).to.be.true;

          sendEmail.sendEmailToOperator.restore();

          getRequestById('foo')
            .then((request) => {
              expect(request.state).to.eq('unavailable');
              expect(parseInt(request.activities.unavailable)).to.lessThan(Date.now());
              expect(request.acceptedOptionIds).to.have.length(1);
              done();
              return;
            })
            .catch((err) => done(err));
        }
      );
    });
  });

  describe('isApplicableToAccept', () => {
    it('should throw if state cannot be moved to accepted', () => {
      expect(() => isApplicableToAccept(requestFixture, [1])).to.throw(Error);
    });

    it('should throw if there are no offers', () => {
      expect(() =>
        isApplicableToAccept(Object.assign({}, requestFixture, { offers: [] }), [0])
      ).to.throw(Error);
    });

    it('should throw if there is already accepted', () => {
      requestFixture.offers[0].accepted = true;
      expect(() => isApplicableToAccept(requestFixture, [0])).to.throw(Error);
    });

    it('should return the request if it can be moved', () => {
      requestFixture.offers[0].accepted = false;
      requestFixture.offers[0].optionIds = ['test1', 'test'];
      requestFixture.state = 'opened';
      const request = isApplicableToAccept(requestFixture, ['test']);
      expect(request).to.deep.eq(requestFixture);
    });

    it('should throw validation error with no present option-id', () => {
      requestFixture.offers[0].accepted = false;
      requestFixture.state = 'opened';
      expect(() => {
        isApplicableToAccept(requestFixture, ['test']);
      }).to.throw(ValidationError);
    });
  });

  describe('checkIfOfferHasActiveReservation', () => {
    it('should check if reservation is active', () => {
      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getReservation/invocations', {
          accountName: 'DEMO',
          propertyCode: 'demo',
          reservationId: 'RES_ID',
        })
        .reply(200, JSON.stringify({ success: true, response: { status: 'active' } }));

      return checkIfOfferHasActiveReservation('DEMO', 'RES_ID', 'demo').then(
        (res) => expect(res).to.be.true
      );
    });

    it('should check if reservation is active', () => {
      nock(AWS_ENDPOINT)
        .post('/2015-03-31/functions/getReservation/invocations', {
          accountName: 'DEMO',
          propertyCode: 'demo',
          reservationId: 'RES_ID',
        })
        .reply(200, JSON.stringify({ success: true, response: { status: 'cancelled' } }));

      return checkIfOfferHasActiveReservation('DEMO', 'RES_ID', 'demo').then(
        (res) => expect(res).to.be.false
      );
    });

    it('should check if reservation is active', () => {
      return checkIfOfferHasActiveReservation('DEMO', '').then((res) => expect(res).to.be.false);
    });
  });
});
