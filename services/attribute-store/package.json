{"name": "quotelier-attribute-store", "version": "1.0.0", "scripts": {"deploy": "sls deploy -s $STAGE -r $REGION --verbose", "shutdown": "sls remove -s $STAGE -r $REGION --verbose"}, "devDependencies": {"@types/aws-lambda": "^8.10.1", "@types/node": "^9.6.5", "@types/raven": "^2.5.1", "aws-sdk": "^2.992.0", "serverless-webpack": "^5.5.4", "ts-loader": "^9.2.6", "typescript": "^4.4.3", "webpack": "^5.53.0"}, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"chunk": "0.0.3", "middy": "^0.36.0", "raven": "^2.6.4"}}