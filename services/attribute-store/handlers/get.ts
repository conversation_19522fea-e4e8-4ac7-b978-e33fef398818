
import { DocumentClient } from 'aws-sdk/clients/dynamodb';
import { init } from '../middlewares';

const chunk = require('chunk');


const { TABLE_NAME } = process.env;

const ddoc = new DocumentClient();

interface AttributeItem {
  id: string;
  name: string;
  value: string;
}

interface Event {
  id: string;
}

interface Response { [k: string]: string }

async function handler(event: Event, context: any): Promise<Response> {
  const { id } = event;

  const { Items } = await ddoc.query({
    TableName: TABLE_NAME,
    KeyConditionExpression: '#id=:id',
    ExpressionAttributeValues: { ':id': id },
    ExpressionAttributeNames: { '#id': 'id' },
  }).promise();

  return (<AttributeItem[]>Items || []).reduce((res, item) => {
    return Object.assign(res, {[item.name]: item.value});
  }, {});
}


module.exports = {handler: init(handler)};