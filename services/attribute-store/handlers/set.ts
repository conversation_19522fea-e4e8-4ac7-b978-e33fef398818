
import { Callback } from 'aws-lambda';
import { DocumentClient } from 'aws-sdk/clients/dynamodb';
import { init } from '../middlewares';

const chunk = require('chunk');


const { TABLE_NAME } = process.env;

const ddoc = new DocumentClient();


interface AttributeItem {
  id: string;
  name: string;
  value: string;
}

interface Event {
  id: string;
  data: { [k: string]: string }
}

interface Response {

}

async function handler(event: Event, context: any): Promise<Response> {
  const { id, data } = event;

  const attrItems = createAttributeItems(id, data);

  const puts = attrItems.map((item) => {
    return {
      PutRequest: {
        Item: item
      }
    }
  });

  const requests = chunk(puts, 20);

  for (let request of requests) {
    await ddoc.batchWrite({ RequestItems: { [TABLE_NAME]: request } }).promise();
  }

  return Promise.resolve({});
}

function createAttributeItems(id: string, data: { [k: string]: string }): AttributeItem[] {
  return Object.keys(data).map((key: string) => {
    let value = data[key];
    const valueType = typeof value;

    if(valueType !== "string" && valueType !== "number") {
      throw new Error(`Value type ${valueType} is not allowed`);
    }

    if (typeof value === "string" && !value) {
      value = null; // dynamodb does not allow empty strings
    }

    return <AttributeItem>{
      id, name: key, value: data[key]
    };
  });
}

module.exports = {handler: init(handler)};