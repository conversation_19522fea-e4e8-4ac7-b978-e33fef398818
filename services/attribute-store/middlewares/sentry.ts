'use strict';
import * as <PERSON> from "raven";
import middy = require("middy");

const { SENTRY_DNS, SENTRY_CAPTURE_WARNINGS, SERVICE, STAGE } = process.env;

Raven.config(SENTRY_DNS, {
  environment: STAGE,
  release: process.env.RELEASE || 'none',
}).install();

/**
 * @returns {object} middy middleware
 */
export function sentryErrorReport(): middy.MiddlewareObject<any, any> {
  return {
    after: (handler, next) => {
      if (SENTRY_CAPTURE_WARNINGS === '1' && handler.response && (handler.response as any).success === false) {
        Raven.captureMessage(
          'Invocation was not successful.',
          createOptions(handler, { level: 'warning' }),
          (err, eventId) => {
            if (err) {
              next(err);
            } else {
              console.log('Capture message: ' + eventId);
              next();
            }
          }
        );
      }

      next();
    },
    onError: (handler, next) => {
      Raven.captureException(handler.error, createOptions(handler), (err, eventId) => {
        if (err) {
          next(err);
        } else {
          console.log('Capture error: ' + eventId);
          next(handler.error);
        }
      });
    },
  };
}

function createOptions(handler: middy.HandlerLambda, options: Raven.CaptureOptions = {}, extra: object = {}): Raven.CaptureOptions {
  return Object.assign(
    {
      extra: Object.assign({
        event: handler.event
      }, extra),
      tags: {
        serviceName: SERVICE,
        stage: STAGE,
        functionName: handler.context.functionName,
        logGroupName: process.env.AWS_LAMBDA_LOG_GROUP_NAME,
      },
    },
    options
  );
}
