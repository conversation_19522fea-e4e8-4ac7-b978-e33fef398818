service:
  name: quotelier-attribute-store

# Add the serverless-webpack plugin
plugins:
  - serverless-webpack

custom:
  webpack:
    packager: 'yarn' 

provider:
  name: aws
  runtime: nodejs14.x
  profile: quotelier
  cfLogs: true
  versionFunctions: false
  deploymentBucket: quotelier-serverless-deployment-bucket
  environment:
    SERVICE: ${self:service}
    SENTRY_DNS: ${env:SENTRY_DNS}
    SENTRY_CAPTURE_WARNINGS: 0
    TABLE_NAME: ${self:service}-${opt:stage}-attributes-table
    RELEASE: ${env:RELEASE, 'none'}
  iamRoleStatements:
    - Effect: Allow
      Action:
      - dynamodb:*
      Resource: "*"

functions:
  set:
    handler: handlers/set.handler
  get:
    handler: handlers/get.handler

resources: 
  Resources:
    AttributesTable:
      Type: AWS::DynamoDB::Table
      Properties:
        TableName: ${self:service}-${opt:stage}-attributes-table
        AttributeDefinitions:
          - AttributeName: id
            AttributeType: S
          - AttributeName: name
            AttributeType: S
        KeySchema:
          - AttributeName: id
            KeyType: HASH
          - AttributeName: name
            KeyType: RANGE
        ProvisionedThroughput:
          ReadCapacityUnits: 5
          WriteCapacityUnits: 5