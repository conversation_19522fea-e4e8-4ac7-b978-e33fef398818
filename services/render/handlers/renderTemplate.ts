import { Callback } from 'aws-lambda';
const _d = require('lodash-deep');
const { validate } = require('@types/quotelier');
const { invokeService } = require('@quotelier/invokeLambda');
const mjml2html = require('mjml').default;
const createHandlebarsEnvironment = require('@quotelier/handlebars');
import * as middlewares from "../middlewares";

interface IEvent {
  accountName: string;
  propertyCode: string;
  context: Context;
  template: QTemplateItem;
}

interface Metadata { [k: string]: any };

interface Context { [k: string]: any };

async function handler(event: IEvent): Promise<QTemplateData> {
  validate(event, {
    type: "object",
    properties: {
      accountName: { type: "string" },
      propertyCode: { type: "string" },
      context: { type: 'object', additionalProperties: {} },
      template: { "$ref": "/QTemplateItem" },
    }
  });

  const handlebars: any = await createHandlebars();

  await registerPropertyPartials(event.accountName, event.template.language, event.propertyCode, handlebars);

  const metadata = compileMetadata(event.template.metadata, event.context, handlebars);
  const context = Object.assign({}, event.context, { metadata });
  const renderer = handlebars.compile(event.template.template);
  let content: string = renderer(context);

  if (isMJMLContent(content)) {
    content = compileMJMLContent(content);
  }

  return {
    metadata,
    content,
    theme: event.template.theme || 'default'
  };
}

function compileMetadata(metadata: Metadata, context: Context, handlebars: any): Metadata {
  return _d.deepMapValues(metadata, value => compileValue(value, context, handlebars));
}

function compileValue(value: string, context: Context, handlebars: any) {
  if (typeof value === 'string') {
    let template = handlebars.compile(value);
    const result = template(context);
    
    // Convert empty strings or 'None' to null for email metadata fields
    if ((result === '' || result === 'None') && 
        (value.includes('email') || value.includes('cc') || value.includes('bcc'))) {
      return null;
    }
    
    return result;
  } else {
    return value;
  }
}

function isMJMLContent(content: string): boolean {
  return content.indexOf('<mjml') !== -1 && content.indexOf('</mjml>') !== -1;
}

function compileMJMLContent(content: string): string {
  const { errors, html } = mjml2html(content);
  (errors || []).forEach(err => console.log('compileMJML', err.message));
  return html;
}

function createHandlebars(): any {
  const handlebars = createHandlebarsEnvironment();
  return handlebars;
}

async function registerPropertyPartials(accountName: string, language: string, propertyCode: string, handlebars: any, theme?: string): Promise<void> {
  const { success, response, error, errorMessage, errorType } = await invokeService('properties-service', 'getPartialTemplates', {
    accountName,
    language,
    propertyCode,
    theme,
  });

  if (success === true) {
    let templateItems: QTemplateItem[] = response;

    templateItems.forEach(({ basename, template }) => {
      if (!basename) {
        throw new Error('Basename is required');
      }
      const partialName = basename.substring(0, basename.lastIndexOf('.'));
      handlebars.registerPartial(partialName, template);
    });

  } else if (success !== false) {
    // unhandled error
    const err = new Error(errorMessage);
    err.name = errorType;
    throw err;
  }
}

module.exports = {handler: middlewares.init(handler)};
