import query from './query';

const graphql = function(event, context, cb) {
  console.log(JSON.stringify(event));
  query(event).then(function(response) {
    console.log('query response')
    console.log(JSON.stringify(response));
    return cb(null, response);
  }).catch(function(error) {
    console.log('query error response')
    console.log(JSON.stringify(error));
    return cb(error);
  });
};

export {
  graphql
};
