import { graphql } from 'graphql';
import Schema from '../../data/Schema';

const query = function(event) {
  if(event.body && event.body.query && event.body.variables){
    let query = event.body.query;
    if (query.hasOwnProperty('query')) {
      query = query.query.replace("\n", ' ', "g");
    }
    return graphql(Schema, query, undefined, undefined, event.body.variables);
  }else{
    return graphql(Schema, event.query, undefined, undefined, event.variables);
  }
};

export default query;
