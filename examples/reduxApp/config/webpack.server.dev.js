import webpack from 'webpack';
import path from 'path';
import paths from './paths';
import babelConfig from './babel.server';

// const debug = process.env.NODE_ENV !== 'production';
const debug = true;

const config = {
  devtool: 'sourcemap',
  entry: path.join(paths.serverAppSrc, 'handler'),
  module: {
    loaders: [{
      test: /\.js?$/,
      loader: require.resolve('babel-loader'),
      exclude: /node_modules/,
      query: babelConfig
    }, {
      test: /\.json$/,
      loaders: ['json-loader']
    }]
  },
  output: {
    path: paths.serverAppBuild,
    filename: 'handler.js',
    library: 'lib',
    libraryTarget: 'commonjs2'
  },
  target: 'node',
  plugins: debug ? [] : [
    new webpack.optimize.DedupePlugin(),
    new webpack.optimize.OccurenceOrderPlugin(),
    new webpack.optimize.UglifyJsPlugin({
      mangle: false,
      sourcemap: false
    })
  ]
};

export default config;