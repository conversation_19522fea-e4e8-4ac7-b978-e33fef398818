import path from 'path';

const config = {
  babelrc: false,
  presets: [
    'es2015',
    'react',
    'stage-0'
  ],
  plugins: [
    // Babel relay plugin transforming Relay.QL queries.
    path.join(__dirname, './babelRelayPlugin'),
    // NOTE: We use absolute paths (using require.resolve) because of the
    // problem with relative paths in Webpack.
    // Static class properties.
    require.resolve('babel-plugin-transform-class-properties'),
    // Share Babel helpers among files - smaller file size.
    require.resolve('babel-plugin-transform-runtime')
  ]
};

export default config;