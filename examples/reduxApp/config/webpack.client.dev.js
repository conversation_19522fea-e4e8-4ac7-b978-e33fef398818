import webpack from 'webpack';
import path from 'path';
import CopyWebpackPlugin from 'copy-webpack-plugin';
import paths from './paths';
import babelConfig from './babel.client';

// const debug = process.env.NODE_ENV !== 'production';
const debug = true;

const config = {
  devtool: 'source-map',
  entry: [
    // These two modules are required for hot code reloading.
    require.resolve('webpack-dev-server/client') + '?/',
    require.resolve('webpack/hot/dev-server'),
    // Provide absolute path to enty file instead of providing context path.
    path.join(paths.clientAppSrc, 'js/main'),
    path.join(paths.dataDir, 'schema.json')
  ],
  module: {
    noParse: [
      /aws-sdk.js/
    ],
    loaders: [{
      test: /aws-sdk.js/,
      loader: 'exports?AWS'
    }, {
      test: /\.json$/,
      loaders: ['json-loader']
    }, {
      test: /.js?$/,
      loader: require.resolve('babel-loader'),
      exclude: /node_modules/,
      query: babelConfig
    }]
  },
  output: {
    path: paths.clientAppBuild,
    pathinfo: true,
    filename: 'js/main.min.js',
    publicPath: '/'
  },
  resolve: {
    alias: {
      'aws-sdk': 'aws-sdk/dist/aws-sdk',
      'modules': path.join(paths.clientAppSrc, '/js/modules'),
      'routes': path.join(paths.clientAppSrc, '/js/routes')
    }
  },
  plugins: [
    new webpack.HotModuleReplacementPlugin(),
    new CopyWebpackPlugin([{
      from: path.join(paths.clientAppSrc, 'index.html'),
      to: path.join(paths.clientAppBuild, 'index.html')
    }]),
    // new webpack.optimize.DedupePlugin(),
    // new webpack.optimize.OccurenceOrderPlugin(),
    // new webpack.optimize.UglifyJsPlugin({
    //   mangle: false,
    //   sourcemap: false
    // })
  ]
};

export default config;
