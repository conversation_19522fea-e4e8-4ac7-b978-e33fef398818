{"name": "quote-builder", "version": "1.0.0", "description": "", "main": "webpack.config.js", "dependencies": {"aws-sdk": "^2.6.0", "bluebird": "^3.4.6", "chalk": "^1.1.3", "dynamodb-data-types": "^3.0.0", "graphql": "^0.7.0", "graphql-relay": "^0.4.2", "lodash": "^4.15.0", "react": "^15.3.1", "react-dom": "^15.3.1", "react-redux": "^4.4.5", "react-relay": "^0.9.2", "react-router": "^2.8.0", "react-router-relay": "^0.13.5", "redux": "^3.6.0", "redux-thunk": "^2.1.0"}, "devDependencies": {"babel-cli": "^6.11.4", "babel-core": "^6.13.2", "babel-loader": "^6.2.5", "babel-plugin-transform-class-properties": "^6.11.5", "babel-plugin-transform-runtime": "^6.12.0", "babel-polyfill": "^6.13.0", "babel-preset-es2015": "^6.13.2", "babel-preset-react": "^6.11.1", "babel-preset-stage-0": "^6.5.0", "babel-relay-plugin": "^0.9.2", "babel-runtime": "^6.11.6", "chokidar": "^1.6.0", "copy-webpack-plugin": "^3.0.1", "exports-loader": "^0.6.3", "express": "^4.14.0", "json-loader": "^0.5.4", "redux-logger": "^2.6.1", "webpack": "^1.13.2", "webpack-dev-middleware": "^1.7.0", "webpack-dev-server": "^1.15.1"}, "scripts": {"build": "./node_modules/.bin/babel-node --presets react,es2015,stage-0 ./scripts/build.js", "start": "./node_modules/.bin/babel-node --presets react,es2015,stage-0 ./scripts/start.js"}, "author": "Quotel<PERSON>", "license": "UNLICENSED"}