import path from 'path';
import chalk from 'chalk';
import config from '../config/webpack.client.dev';
import paths from '../config/paths';
import webpack from 'webpack';
import express from 'express';
import WebpackDevServer from 'webpack-dev-server';
import {
  clearConsole
} from './utils';

function startClient() {
  const compiler = webpack(config);
  const server = new WebpackDevServer(compiler, {
    hot: true,
    publicPath: config.output.publicPath,
    historyApiFallback: true,
    quiet: true,
    watchOptions: {
      ignored: /node_modules/
    }
  });
  server.listen(3000, function(err) {
    if (err) {
      return console.log(err);
    }
    clearConsole();
    console.log(chalk.cyan('Starting the development server...'));
    console.log();
  });
}

export default startClient;