import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import Schema from '../data/Schema';
import { graphql }  from 'graphql';
import { introspectionQuery, printSchema } from 'graphql/utilities';

function buildSchema() {
  console.log(chalk.cyan('Building schema...'));
  graphql(Schema, introspectionQuery).then(function(result) {
    fs.writeFileSync(
      path.join(__dirname, '../data/schema.json'),
      JSON.stringify(result, null, 2)
    );
    fs.writeFileSync(
      path.join(__dirname, '../data/schema.graphql'),
      printSchema(Schema)
    );
    console.log(chalk.green('Schema successfully built!'));
  });
}

export default buildSchema;