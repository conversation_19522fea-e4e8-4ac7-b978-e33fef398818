import buildClient from './buildClient';
import buildSchema from './buildSchema';
import buildServer from './buildServer';

const buildSyntax = 'npm run build [server | client | schema]';
const arg = process.argv[2];

if (arg === 'client') {
  buildClient();
}
else if (arg === 'schema') {
  buildSchema();
}
else if (arg === 'server') {
  buildServer();
}
else if (arg === undefined) {
  console.error(
    `I don't know what do you want to build:\n` + buildSyntax
  );
  process.exit(1);
}
else {
  console.error(
    `I don't know how to build "${arg}"\n` + buildSyntax
  );
  process.exit(1);
}