import chalk from 'chalk';
import config from '../config/webpack.server.dev';
import webpack from 'webpack';

function buildServer() {
  console.log(chalk.cyan('Building server application...'));
  webpack(config).run(function(err, stats) {
    if (err) {
      console.log(chalk.red(err.message || err));
      process.exit(1);
    }
    else {
      console.log(chalk.green('Server application successfully built!'));
    }
  });
}

export default buildServer;