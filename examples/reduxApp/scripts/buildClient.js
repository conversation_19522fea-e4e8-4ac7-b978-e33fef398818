import chalk from 'chalk';
import config from '../config/webpack.client.dev';
import webpack from 'webpack';

function buildServer() {
  console.log(chalk.cyan('Building client application...'));
  webpack(config).run(function(err, stats) {
    if (err) {
      console.log(chalk.red(err.message || err));
      process.exit(1);
    }
    else {
      console.log(chalk.green('Client application successfully built!'));
    }
  });
}

export default buildServer;