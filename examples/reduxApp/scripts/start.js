import startClient from './startClient';
import startServer from './startServer';
import startSchema from './startSchema';

const buildSyntax = 'npm run start [client | server | schema]';
const arg = process.argv[2];

if (arg === 'client') {
  startClient();
}
else if (arg === 'server') {
  // startServer();
}
else if (arg === 'schema') {
  startSchema();
}
else if (arg === undefined) {
  console.error(
    `I don't know what do you want to start:\n` + buildSyntax
  );
  process.exit(1);
}
else {
  console.error(
    `I don't know how to start "${arg}"\n` + buildSyntax
  );
  process.exit(1);
}