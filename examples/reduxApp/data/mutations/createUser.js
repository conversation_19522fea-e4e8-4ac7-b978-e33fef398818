// import {
//   GraphQLNonNull,
//   GraphQLString
// } from 'graphql';

// import {
//   mutationWithClientMutationId
// } from 'graphql-relay';

// import UserConnection from '../UserConnection';
// import List from '../List';
// import {
//   getList,
//   createUser
// } from '../database';

// const CreateUser = mutationWithClientMutationId({
//   name: 'CreateUser',
//   inputFields: {
//     firstName: {
//       type: new GraphQLNonNull(GraphQLString)
//     },
//     lastName: {
//       type: new GraphQLNonNull(GraphQLString)
//     },
//     email: {
//       type: new GraphQLNonNull(GraphQLString)
//     }
//   },
//   outputFields: {
//     userEdge: {
//       type: UserConnection.edgeType,
//       resolve: (user) => ({
//         node: user,
//         cursor: user.id
//       })
//     },
//     list: {
//       type: List,
//       resolve: () => getList()
//     }
//   },
//   mutateAndGetPayload: (input) => {
//     const {
//       firstName,
//       lastName,
//       email
//     } = input;

//     const user = {
//       firstName,
//       lastName,
//       email
//     };

//     return createUser(user);
//   }
// });

// export default CreateUser;