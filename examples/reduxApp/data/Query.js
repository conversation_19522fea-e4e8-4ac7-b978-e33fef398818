import {
  // These are the basic GraphQL types
  GraphQLInt,
  GraphQLFloat,
  GraphQLString,
  GraphQLList,
  GraphQLObjectType,
  GraphQLEnumType,

  // This is used to create required fields and arguments
  GraphQLNonNull,

  // This is the class we need to create the schema
  GraphQLSchema,

  // This function is used execute GraphQL queries
  graphql
} from 'graphql';


//================
// Resolve F(x)ns
//

import {
  getAccount,
  getProperties,
  getProperty,
  getRooms,
  getRoom,
  getRates,
  getRate,
  getServices,
  getService
} from './Repository';


//================
// Data Objects
//

import Account from './Account';
import Property from './Property';
import Room from './Room';
import Rate from './Rate';
import Service from './Service';


//================
// Qurier Builder
//

export default new GraphQLObjectType({
  name: 'RootQueries',
  fields: () => ({
    account: {
      type: Account,
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        }
      },
      resolve: (source, args) => getAccount(args.accountName)
    },
    properties: {
      type: new GraphQLList(Property),
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        }
      },
      resolve: (source, args) => getProperties(args)
    },
    property: {
      type: Property,
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        }
      },
      resolve: (source, args) => getProperty(args)
    },
    rooms: {
      type: new GraphQLList(Room),
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        }
      },
      resolve: (source, args) => getRooms(args)
    },
    room: {
      type: Room,
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString)
        }
      },
      resolve: (source, args) => getRoom(args)
    },
    rates: {
      type: new GraphQLList(Rate),
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString)
        }
      },
      resolve: (source, args) => getRates(args)
    },
    rate: {
      type: Rate,
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        rateId: {
          type: new GraphQLNonNull(GraphQLInt)
        }
      },
      resolve: (source, args) => getRate(args)
    },
    services: {
      type: new GraphQLList(Service),
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        rateId: {
          type: new GraphQLNonNull(GraphQLInt)
        }
      },
      resolve: (source, args) => getServices(args)
    },
    service: {
      type: Service,
      args: {
        accountName: {
          type: new GraphQLNonNull(GraphQLString)
        },
        language: {
          type: new GraphQLNonNull(GraphQLString)
        },
        propertyCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        roomCode: {
          type: new GraphQLNonNull(GraphQLString)
        },
        rateId: {
          type: new GraphQLNonNull(GraphQLInt)
        },
        serviceId: {
          type: new GraphQLNonNull(GraphQLInt)
        }
      },
      resolve: (source, args) => getService(args)
    }
  })
});
