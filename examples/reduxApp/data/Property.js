import {
  GraphQLID,
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLString,
  GraphQLFloat
} from 'graphql';

import Photo from './Photo';
import Contact from './propertyModels/Contact';
import Location from './propertyModels/Location';
import Children from './propertyModels/Children';
import Settings from './propertyModels/Settings';
import Operation from './propertyModels/Operation';

const Property = new GraphQLObjectType({
  name: 'Property',
  description: 'Quotelier Property Class',
  fields: () => ({
    code: {
      type: new GraphQLNonNull(GraphQLID)
    },
    name: {
      type: GraphQLString
    },
    description: {
      type: GraphQLString
    },
    bookurl: {
      type: GraphQLString
    },
    url: {
      type: GraphQLString
    },
    type: {
      type: GraphQLString
    },
    currency: {
      type: GraphQLString
    },
    logourl: {
      type: GraphQLString
    },
    facilities: {
      type: new GraphQLList(GraphQLString)
    },
    rating: {
      type: GraphQLFloat
    },
    contact: {
      type: Contact
    },
    location: {
      type: Location
    },
    children: {
      type: Children
    },
    settings: {
      type: Settings
    },
    operation: {
      type: Operation
    },
    photos: {
      type: new GraphQLList(Photo)
    }
  })
});

export default Property;
