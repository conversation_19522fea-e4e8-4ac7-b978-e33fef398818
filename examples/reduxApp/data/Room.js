import {
  GraphQLID,
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLString,
  GraphQLFloat,
  GraphQLBoolean
} from 'graphql';

import Photo from './Photo';
import Capacity from './roomModels/Capacity';

const Room = new GraphQLObjectType({
  name: 'Room',
  description: 'Quotelier Room Class',
  fields: () => ({
    code: {
      type: new GraphQLNonNull(GraphQLID)
    },
    name: {
      type: GraphQLString
    },
    description: {
      type: GraphQLString
    },
    amenities: {
      type: new GraphQLList(GraphQLString)
    },
    active: {
      type: GraphQLBoolean
    },
    capacity: {
      type: Capacity
    },
    photos: {
      type: new GraphQLList(Photo)
    }
  })
});

export default Room;
