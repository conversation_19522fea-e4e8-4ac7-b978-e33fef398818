import {
  GraphQLObjectType,
  GraphQLString,
  GraphQLFloat
} from 'graphql';

const Location = new GraphQLObjectType({
  name: 'Location',
  fields: () => ({
    lat: {
      type: GraphQLFloat
    },
    lon: {
      type: GraphQLFloat
    },
    utc_offset: {
      type: GraphQLFloat
    },
    timezone: {
      type: GraphQLString
    },
    name: {
      type: GraphQLString
    },
    address: {
      type: GraphQLString
    },
    zip: {
      type: GraphQLString
    },
    country: {
      type: GraphQLString
    }
  })
});

export default Location;
