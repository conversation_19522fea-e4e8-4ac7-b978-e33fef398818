import {
  GraphQLID,
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat,
  GraphQLBoolean
} from 'graphql';

const Service = new GraphQLObjectType({
  name: 'Service',
  description: 'Quotelier Service Class',
  fields: () => ({
    id: {
      type: new GraphQLNonNull(GraphQLID)
    },
    name: {
      type: GraphQLString
    },
    description: {
      type: GraphQLString
    },
    extra_price: {
      type: GraphQLFloat
    },
    per_day: {
      type: GraphQLInt
    },
    per_adult: {
      type: GraphQLInt
    },
    per_child: {
      type: GraphQLInt
    },
    per_infant: {
      type: GraphQLInt
    },
    per_room: {
      type: GraphQLInt
    },
    required: {
      type: GraphQLInt
    },
    max_quantity: {
      type: GraphQLInt
    },
    fromd: {
      type: GraphQLString
    },
    tod: {
      type: GraphQLString
    },
    photo: {
      type: GraphQLString
    },
    excl: {
      type: new GraphQLList(GraphQLString)
    }
  })
});

export default Service;
