import {
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat,
  GraphQLBoolean
} from 'graphql';


const Constraints = new GraphQLObjectType({
  name: 'Constraints',
  description: 'Quotelier Constraints Class',
  fields: () => ({
    expiration: {
      type: GraphQLString
    },
    earlyBookLimit: {
      type: GraphQLInt
    },
    freeCancelDays: {
      type: GraphQLInt
    }
  })
});

export default Constraints
