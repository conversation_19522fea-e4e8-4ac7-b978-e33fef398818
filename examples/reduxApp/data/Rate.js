import {
  Graph<PERSON><PERSON>,
  GraphQLList,
  GraphQLNonNull,
  GraphQLObjectType,
  GraphQLString,
  GraphQLInt,
  GraphQLFloat,
  GraphQLBoolean
} from 'graphql';

import Constraints from './rateModels/Constraints';
import Policies from './rateModels/Policies';

const Rate = new GraphQLObjectType({
  name: 'Rate',
  description: 'Quotelier Rate Class',
  fields: () => ({
    id: {
      type: new GraphQLNonNull(GraphQLID)
    },
    room: {
      type: GraphQLString
    },
    name: {
      type: GraphQLString
    },
    board: {
      type: GraphQLInt
    },
    active: {
      type: GraphQLInt
    },
    public: {
      type: GraphQLInt
    },
    parent: {
      type: GraphQLInt
    },
    virtual: {
      type: GraphQLString
    },
    currency: {
      type: GraphQLString
    },
    roomName: {
      type: GraphQLString
    },
    parentRate: {
      type: GraphQLInt
    },
    fromd: {
      type: GraphQLString
    },
    tod: {
      type: GraphQLString
    },
    constraints: {
      type: Constraints
    },
    description: {
      type: GraphQLString
    },
    presentation: {
      type: GraphQLString
    },
    policies: {
      type: Policies
    }
  })
});

export default Rate;
