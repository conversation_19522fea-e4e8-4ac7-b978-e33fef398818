schema {
  query: RootQueries
}

type Account {
  name: String
  accountName: String!
}

type Capacity {
  min_pers: Int
  max_pers: Int
  max_adults: Int
  children_allowed: Boolean
}

type Children {
  allowed: Int
  age_from: Int
  age_to: Int
}

type Constraints {
  expiration: String
  earlyBookLimit: Int
  freeCancelDays: Int
}

type Contact {
  tel: String
  fax: String
  email: String
  skype: String
}

type Location {
  lat: Float
  lon: Float
  utc_offset: Float
  timezone: String
  name: String
  address: String
  zip: String
  country: String
}

type Operation {
  checkout_time: String
  checkin_time: String
  open_from: String
  open_to: String
}

type Photo {
  title: String
  xsmall: String
  small: String
  medium: String
  large: String
}

type Policies {
  cancellation: String
  payment: String
}

type Property {
  code: ID!
  name: String
  description: String
  bookurl: String
  url: String
  type: String
  currency: String
  logourl: String
  facilities: [String]
  rating: Float
  contact: Contact
  location: Location
  children: Children
  settings: Settings
  operation: Operation
  photos: [Photo]
}

type Rate {
  id: ID!
  room: String
  name: String
  board: Int
  active: Int
  public: Int
  parent: Int
  virtual: String
  currency: String
  roomName: String
  parentRate: Int
  fromd: String
  tod: String
  constraints: Constraints
  description: String
  presentation: String
  policies: Policies
}

type Room {
  code: ID!
  name: String
  description: String
  amenities: [String]
  active: Boolean
  capacity: Capacity
  photos: [Photo]
}

type RootQueries {
  account(accountName: String!): Account
  properties(accountName: String!, language: String!): [Property]
  property(accountName: String!, language: String!, propertyCode: String!): Property
  rooms(accountName: String!, language: String!, propertyCode: String!): [Room]
  room(accountName: String!, language: String!, propertyCode: String!, roomCode: String!): Room
  rates(accountName: String!, language: String!, propertyCode: String!, roomCode: String!): [Rate]
  rate(accountName: String!, language: String!, propertyCode: String!, roomCode: String!, rateId: Int!): Rate
  services(accountName: String!, language: String!, propertyCode: String!, roomCode: String!, rateId: Int!): [Service]
  service(accountName: String!, language: String!, propertyCode: String!, roomCode: String!, rateId: Int!, serviceId: Int!): Service
}

type Service {
  id: ID!
  name: String
  description: String
  extra_price: Float
  per_day: Int
  per_adult: Int
  per_child: Int
  per_infant: Int
  per_room: Int
  required: Int
  max_quantity: Int
  fromd: String
  tod: String
  photo: String
  excl: [String]
}

type Settings {
  nights_min: Int
  nights_max: Int
  rooms_max: Int
}
