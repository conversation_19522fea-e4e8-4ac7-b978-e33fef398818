import Promise from 'bluebird';
import S3 from 'aws-sdk/clients/s3';
import DynamoDB from 'aws-sdk/clients/dynamodb';
import { AttributeValue } from 'dynamodb-data-types';
import config from '../config/config';


const s3 = new S3({
  region: config.region
});

const ddb = new DynamoDB({
  region: config.region
});

const get = Promise.promisify(s3.getObject, { context: s3 });
const list = Promise.promisify(s3.listObjectsV2, { context: s3 });

const getItem = Promise.promisify(ddb.getItem, { context: ddb });

function getAccount(args){
  console.log('getAccount input');
  console.log(JSON.stringify(args));
  return getItem({
    TableName: config.accountsTable,
    Key: {
      accountName: {
        S: typeof args === 'string' ? args : args.id
      }
    }
  }).then(function(response){
    console.log('getAccount output');
    console.log(response);
    console.log('getAccount unwrapped output');
    console.log(AttributeValue.unwrap(response.Item));
    return AttributeValue.unwrap(response.Item);
  });
}

function getProperty(args){
  console.log('getProperty');
  console.log(JSON.stringify(args));
  return get({
    Bucket: config.propertiesBucket,
    Key: `${args.accountName}/${args.language}/${args.propertyCode}`
  }).then(function(s3Object){
    return JSON.parse(s3Object.Body.toString('utf8'));
  })
}

function getProperties(args){
  console.log('getProperties');
  console.log(JSON.stringify(args));
  return args.accountName && args.language ? list({
    Bucket: config.propertiesBucket,
    Prefix: `${args.accountName}/${args.language}/`,
    Delimiter: '/'
  }).then(function(result){
    return Promise.map(result.Contents, function(propertyMetadata){
      return get({
        Bucket: config.propertiesBucket,
        Key: propertyMetadata.Key
      }).then(function(s3Object){
        return JSON.parse(s3Object.Body.toString('utf8'));
      });
    }).then(function(result){
      console.log('fetched all properties?');
      console.log(JSON.stringify(result));
      return result;
    })
  }) : Promise.resolve([]);
}

function getRoom(args){
  console.log('getRoom');
  console.log(JSON.stringify(args));
  return get({
    Bucket: config.propertiesBucket,
    Key: `${args.accountName}/${args.language}/${args.propertyCode}/${args.roomCode}`
  }).then(function(s3Object){
    return JSON.parse(s3Object.Body.toString('utf8'));
  })
}

function getRooms(args){
  console.log('getRooms');
  console.log(JSON.stringify(args));
  return args.accountName && args.language && args.propertyCode ? list({
    Bucket: config.propertiesBucket,
    Prefix: `${args.accountName}/${args.language}/${args.propertyCode}/`,
    Delimiter: '/'
  }).then(function(result){
    return Promise.map(result.Contents, function(roomMetaData){
      return get({
        Bucket: config.propertiesBucket,
        Key: roomMetaData.Key
      }).then(function(s3Object){
        return JSON.parse(s3Object.Body.toString('utf8'));
      });
    }).then(function(result){
      console.log('fetched all rooms?');
      console.log(JSON.stringify(result));
      return result;
    })
  }) : Promise.resolve([]);
}

function getRate(args){
  console.log('getRate');
  console.log(JSON.stringify(args));
  return get({
    Bucket: config.propertiesBucket,
    Key: `${args.accountName}/${args.language}/${args.propertyCode}/${args.roomCode}/${args.rateId}`
  }).then(function(s3Object){
    return JSON.parse(s3Object.Body.toString('utf8'));
  })
}

function getRates(args){
  console.log('getRates');
  console.log(JSON.stringify(args));
  return args.accountName && args.language && args.propertyCode ? list({
    Bucket: config.propertiesBucket,
    Prefix: `${args.accountName}/${args.language}/${args.propertyCode}/${args.roomCode}/`,
    Delimiter: '/'
  }).then(function(result){
    return Promise.map(result.Contents, function(rateMetaData){
      return get({
        Bucket: config.propertiesBucket,
        Key: rateMetaData.Key
      }).then(function(s3Object){
        return JSON.parse(s3Object.Body.toString('utf8'));
      });
    }).then(function(result){
      console.log('fetched all rates?');
      console.log(JSON.stringify(result));
      return result;
    })
  }) : Promise.resolve([]);
}

function getService(args){
  console.log('getService');
  console.log(JSON.stringify(args));
  return get({
    Bucket: config.propertiesBucket,
    Key: `${args.accountName}/${args.language}/${args.propertyCode}/${args.roomCode}/${args.rateId}/${args.serviceId}`
  }).then(function(s3Object){
    return JSON.parse(s3Object.Body.toString('utf8'));
  })
}

function getServices(args){
  console.log('getServices');
  console.log(JSON.stringify(args));
  return args.accountName && args.language && args.propertyCode ? list({
    Bucket: config.propertiesBucket,
    Prefix: `${args.accountName}/${args.language}/${args.propertyCode}/${args.roomCode}/${args.rateId}/`,
    Delimiter: '/'
  }).then(function(result){
    return Promise.map(result.Contents, function(serviceMetaData){
      return get({
        Bucket: config.propertiesBucket,
        Key: serviceMetaData.Key
      }).then(function(s3Object){
        return JSON.parse(s3Object.Body.toString('utf8'));
      });
    }).then(function(result){
      console.log('fetched all services?');
      console.log(JSON.stringify(result));
      return result;
    })
  }) : Promise.resolve([]);
}

export {
  getProperty,
  getProperties,
  getRoom,
  getRooms,
  getRate,
  getRates,
  getService,
  getServices,
  getAccount
};
