import _ from 'lodash';
import {
  browserHistory,
  Router
} from 'react-router';
import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux'
import configureStore  from './store/configureStore'
import config from '../../../config/config';
import routes from './routes'


const store = configureStore()

let ComponentEl = (
  <div>
    <Router history={browserHistory} routes={routes} />
  </div>
);


ReactDOM.render(
  <Provider store={store}>
    {ComponentEl}
  </Provider>,
  document.getElementById('app')
);
