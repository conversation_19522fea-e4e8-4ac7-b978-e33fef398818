import React, { Component, PropTypes } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router';
import { getAccount } from 'modules/accounts/actions/account.js';

export class Application extends Component {
  constructor(props) {
    super(props)
  }

  componentWillMount() {
    this.props.dispatch(getAccount(this.props.params.accountName))
  }

  render() {
    const {
      account
    } = this.props || {};

    return (
      <div>
        <h2>({account.accountName}) {account.name}</h2>
        <Link to={`/${account.accountName}/en`}>English</Link>
        <br/>
        <Link to={`/${account.accountName}/el`}>Greek</Link>
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { account } = state

  return {
    account
  }
}

export default connect(
  mapStateToProps
)(Application)
