import React, { Component, PropTypes } from 'react';
import { connect } from 'react-redux';
import { getProperty } from 'modules/properties/actions/property.js';

export class Property extends Component {
  constructor(props) {
    super(props)
  }

  componentWillMount() {
    this.props.dispatch(getProperty(this.props.params))
  }

  render() {
    const {
      params,
      account,
      properties
    } = this.props || {};

    const property = properties.selected;

    return (
      <div>
        <h1>Property for {params.accountName} {params.language}</h1>
        {property != null ? <div>{JSON.stringify(property)}</div> : <div>Loading...</div>}
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { account, properties } = state

  return {
    account,
    properties
  }
}

export default connect(
  mapStateToProps
)(Property)
