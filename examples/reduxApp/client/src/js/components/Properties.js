import React, { Component, PropTypes } from 'react';
import { connect } from 'react-redux';
import { Link } from 'react-router';
import { getProperties } from 'modules/properties/actions/properties.js';

export class Properties extends Component {
  constructor(props) {
    super(props)
  }

  componentWillMount() {
    this.props.dispatch(getProperties(this.props.params))
  }

  render() {
    const {
      params,
      account,
      properties
    } = this.props || {};

    const content = properties.list && properties.list.length > 0 ? properties.list.map((property) => {
      return (
        <p key={property.code}>
          <Link to={`/${params.accountName}/${params.language}/${property.code}`}><strong>{property.name}</strong></Link>
        </p>
      );
    }) : <p>No Properties</p>;

    return (
      <div>
        <h1>Property List for {params.accountName} {params.language}</h1>
        {content}
      </div>
    );
  }
}

function mapStateToProps(state) {
  const { account, properties } = state

  return {
    account,
    properties
  }
}

export default connect(
  mapStateToProps
)(Properties)
