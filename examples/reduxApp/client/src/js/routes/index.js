import React from 'react'
import { Router, Route, IndexRoute } from 'react-router'

import App from 'routes/App';
import Application from '../components/Application';
import Properties from '../components/Properties';
import Property from '../components/Property';


export default (
  <Route path="/">
    <Route
      path="/:accountName"
      component={Application}
      />
    <Route
      path="/:accountName/:language"
      component={Properties}
      />
    <Route
      path="/:accountName/:language/:propertyCode"
      component={Property}
      />
  </Route>
);
