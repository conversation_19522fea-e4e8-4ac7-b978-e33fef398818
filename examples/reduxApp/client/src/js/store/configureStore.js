import { createStore, applyMiddleware, compose } from 'redux'
import thunkMiddleware from 'redux-thunk'
import createLogger from 'redux-logger'
import rootReducer from 'modules/reducers'

export default function configureStore(initialState) {
  let enhancer;
  let middleware = applyMiddleware(thunkMiddleware, createLogger());
  enhancer = compose(middleware);

  const store = createStore(rootReducer, initialState, enhancer);

  return store;
}
