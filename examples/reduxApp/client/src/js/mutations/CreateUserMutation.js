// import Relay from 'react-relay';

// class CreateUserMutation extends Relay.Mutation {
//   getMutation() {
//     return Relay.QL`
//       mutation {
//         createUser
//       }
//     `;
//   }

//   getVariables() {
//     return {
//       firstName: this.props.firstName,
//       lastName: this.props.lastName,
//       email: this.props.email
//     };
//   }

//   getFatQuery() {
//     return Relay.QL`
//       fragment on CreateUserPayload {
//         userEdge,
//         list {
//           userConnection
//         }
//       }
//     `;
//   }

//   getConfigs() {
//     return [{
//       type: 'RANGE_ADD',
//       parentName: 'list',
//       parentID: this.props.list.id,
//       connectionName: 'userConnection',
//       edgeName: 'userEdge',
//       rangeBehaviors: {
//         '': 'append'
//       },
//     }];
//   }

//   getOptimisticResponse() {
//     return {
//       userEdge: {
//         node: {
//           firstName: this.props.firstName,
//           lastName: this.props.lastName,
//           email: this.props.email
//         }
//       }
//     };
//   }
// }

// export default CreateUserMutation;