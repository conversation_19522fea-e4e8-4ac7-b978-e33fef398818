import AWS from 'aws-sdk';
import Promise from 'bluebird';
import {
  each,
  has
} from 'lodash';

class ServerlessNetworkLayer {
  constructor(options) {
    const {
      functionName,
      accessKeyId,
      secretAccessKey,
      region
    } = options;

    this._params = {
      FunctionName: functionName
    };

    const lambda = new AWS.Lambda({
      apiVersion: '2015-03-31',
      accessKeyId,
      secretAccessKey,
      region
    });

    this._invoke = Promise.promisify(lambda.invoke, {
      context: lambda
    });
  }

  _createRequestError(request, responseStatus, payload) {
    return new Error('GraphQL Server: ' +
      _.map(payload.errors, 'message').join('\n')
    );
  }

  _sendMutation(request) {
    let params;
    const files = request.getFiles();
    if (files) {
      // TODO: Implement files upload.
      params = {
        ...this.params
      };
    }
    else {
      params = {
        ...this._params,
        Payload: JSON.stringify({
          query: request.getQueryString(),
          variables: request.getVariables()
        })
      };
    }
    return this._invoke(params);
  }

  _sendQuery(request) {
    const params = {
      ...this._params,
      Payload: JSON.stringify({
        query: request.query,
        variables: request.variables
      })
    };
    console.log('Params sent to invoke GraphQL Lambda');
    console.log(params)
    return this._invoke(params).then(function(result){
      console.log('Got result from GraphQL Server');
      console.log(result);
      return result;
    });
  }

  sendMutation(request) {
    return this._sendMutation(request).then(response => {
      return JSON.parse(response.Payload);
    }).then(payload => {
      if (has(payload, 'errors')) {
        const err = this._createRequestError(request, '200', payload);
        request.reject(err);
      }
      else {
        request.resolve({
          response: payload.data
        });
      }
    }).catch(
      err => request.reject(err)
    );
  }

  sendQueries(requests) {
    return Promise.all(requests.map(request => {
      this._sendQuery(request).then(response => {
        return JSON.parse(response.Payload);
      }).then(payload => {
        if (has(payload, 'errors')) {
          const err = this._createRequestError(request, '200', payload);
          request.reject(err);
        }
        else if (!has(payload, 'data')) {
          request.reject(
            new Error(
              `Server response was missing for query \`${request.getDebugName()}\`.`
            )
          );
        }
        else {
          request.resolve({
            response: payload.data
          });
        }
      }).catch(
        err => request.reject(err)
      );
    }));
  }

  supports(...options) {
    return false;
  }
}

export default ServerlessNetworkLayer;
