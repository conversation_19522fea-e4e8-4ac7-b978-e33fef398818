import * as ActionTypes from '../constants';
import config from '../../../../../../config/config';
import ServerlessNetworkLayer from '../../ServerlessNetworkLayer';

function accountReq(){
  return {
    type: ActionTypes.ACCOUNT_REQ,
  }
}

function accountReqSuccess(data){
  console.log(data)
  return {
    type: ActionTypes.ACCOUNT_SUCCESS,
    account: data.account
  }
}

function accountReqFail(message){
  return {
    type: ActionTypes.ACCOUNT_FAIL,
    errorMessage: message
  }
}

export function getAccount(payload){
  const query = `
    query getAccount($accountName: String!) {
      account(accountName: $accountName) {
        accountName,
        name
      }
    }
  `

  const variables = {
    accountName: payload
  }

  const request = {
    query,
    variables
  }

  return dispatch => {

    dispatch(accountReq())

    const setup = _.pick(config, ['functionName', 'accessKeyId', 'secretAccessKey', 'region'])
    const serverlessNetworkLayer =  new ServerlessNetworkLayer(setup)
    return serverlessNetworkLayer._sendQuery(request).then((response) => {
      const result = JSON.parse(response.Payload)
      if (result.errors){
        //not implemented
        // dispatch(accountReqFail(result.errors))
      }else{
        dispatch(accountReqSuccess(result.data))
      }
    })
  }

}
