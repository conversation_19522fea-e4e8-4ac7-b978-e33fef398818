import * as ActionTypes from '../constants';

export default function account(state = {
    requesting: false,
    name: null,
    accountName: null
  },
  action){
    switch (action.type) {
      case ActionTypes.ACCOUNT_REQ:
        return Object.assign({}, state, {
          requesting: true
        })
      case ActionTypes.ACCOUNT_SUCCESS:
        return Object.assign({}, state, {
          requesting: false,
          name: action.account.name,
          accountName: action.account.accountName
        })
      case ActionTypes.ACCOUNT_FAIL:
        return Object.assign({}, state, {
          requesting: false,
          name: null,
          accountName: null
        })
      default:
        return state
    }
  }
