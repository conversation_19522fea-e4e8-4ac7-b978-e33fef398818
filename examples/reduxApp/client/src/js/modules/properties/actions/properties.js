import * as ActionTypes from '../constants';
import config from '../../../../../../config/config';
import ServerlessNetworkLayer from '../../ServerlessNetworkLayer';

function propertiesReq(){
  return {
    type: ActionTypes.PROPERTIES_REQ,
  }
}

function propertiesReqSuccess(data){
  return {
    type: ActionTypes.PROPERTIES_SUCCESS,
    properties: data.properties
  }
}

function propertiesReqFail(message){
  return {
    type: ActionTypes.PROPERTIES_FAIL,
    errorMessage: message
  }
}

export function getProperties(payload){
  const query = `
    query getProperties($accountName: String!, $language: String!) {
      properties(accountName: $accountName, language: $language) {
        code,
        name,
        url
      }
    }
  `
  const variables = {
    "accountName": payload.accountName,
    "language": payload.language
  }

  const request = {
    query,
    variables
  }

  return dispatch => {

    dispatch(propertiesReq())

    const setup = _.pick(config, ['functionName', 'accessKeyId', 'secretAccessKey', 'region'])
    const serverlessNetworkLayer =  new ServerlessNetworkLayer(setup)
    return serverlessNetworkLayer._sendQuery(request).then((response) => {
      const result = JSON.parse(response.Payload)
      console.log(result)
      if (result.errors){
        //not implemented
        // dispatch(propertiesReqFail(result.errors))
      }else{
          dispatch(propertiesReqSuccess(result.data))
      }
    })
  }

}
