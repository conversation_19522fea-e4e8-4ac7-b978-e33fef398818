import * as ActionTypes from '../constants';
import config from '../../../../../../config/config';
import ServerlessNetworkLayer from 'modules/ServerlessNetworkLayer';
import { propertyRequest } from  'modules/mainQueries';

function propertyReq(){
  return {
    type: ActionTypes.PROPERTY_REQ,
  }
}

function propertyReqSuccess(data){
  return {
    type: ActionTypes.PROPERTY_SUCCESS,
    property: data.property
  }
}

function propertyReqFail(message){
  return {
    type: ActionTypes.PROPERTY_FAIL,
    errorMessage: message
  }
}

export function getProperty(payload){

  const request = propertyRequest(payload)
  return dispatch => {

    dispatch(propertyReq())

    const setup = _.pick(config, ['functionName', 'accessKeyId', 'secretAccessKey', 'region'])
    const serverlessNetworkLayer =  new ServerlessNetworkLayer(setup)
    return serverlessNetworkLayer._sendQuery(request).then((response) => {
      const result = JSON.parse(response.Payload)
      console.log(result)
      if (result.errors){
        //not implemented
        // dispatch(propertiesReqFail(result.errors))
      }else{
          dispatch(propertyReqSuccess(result.data))
      }
    })
  }

}
