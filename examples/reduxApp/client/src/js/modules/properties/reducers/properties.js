import * as ActionTypes from '../constants';

export default function properties(state = {
    requesting: false,
    list: [],
    selected: null
  },
  action){
    switch (action.type) {
      case ActionTypes.PROPERTIES_REQ:
        return Object.assign({}, state, {
          requesting: true
        })
      case ActionTypes.PROPERTIES_SUCCESS:
        return Object.assign({}, state, {
          requesting: false,
          list: action.properties
        })
      case ActionTypes.PROPERTIES_FAIL:
        return Object.assign({}, state, {
          requesting: false,
          list: []
        })
      case ActionTypes.PROPERTY_SUCCESS:
        return Object.assign({}, state, {
          selected: action.property
        })
      default:
        return state
    }
  }
