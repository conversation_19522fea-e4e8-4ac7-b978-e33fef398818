//PROPERTIES

const propertiesQuery = `
  query getProperties($accountName: String!, $language: String!) {
    properties(accountName: $accountName, language: $language) {
      code,
      name,
      description
    }
  }
`
const propertiesVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language
  }
}

const propertiesRequest = (payload) => {
  return {
    query: propertiesQuery,
    variables: propertiesVariables(payload)
  }
}

//PROPERTY

const propertyQuery = `
  query getProperty($accountName: String!, $language: String!, $propertyCode: String!) {
    property(accountName: $accountName, language: $language, propertyCode: $propertyCode) {
      code,
      name,
      description
    }
  }
`
const propertyVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode
  }
}

const propertyRequest = (payload) => {
  return {
    query: propertyQuery,
    variables: propertyVariables(payload)
  }
}


//ROOMS

const roomsQuery = `
  query getRooms($accountName: String!, $language: String!, $propertyCode: String!) {
    rooms(accountName: $accountName, language: $language, propertyCode: $propertyCode) {
      code,
      name,
      description
    }
  }
`
const roomsVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode
  }
}

const roomsRequest = (payload) => {
  return {
    query: roomsQuery,
    variables: roomsVariables(payload)
  }
}


//ROOM

const roomQuery = `
  query getRoom($accountName: String!, $language: String!, $propertyCode: String!, $roomCode: String!) {
    room(accountName: $accountName, language: $language, propertyCode: $propertyCode, roomCode: $roomCode) {
      code,
      name,
      description
    }
  }
`
const roomVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode,
    "roomCode": payload.roomCode
  }
}

const roomRequest = (payload) => {
  return {
    query: roomQuery,
    variables: roomVariables(payload)
  }
}



//RATES

const ratesQuery = `
  query getRates($accountName: String!, $language: String!, $propertyCode: String!, $roomCode: String!) {
    rates(accountName: $accountName, language: $language, propertyCode: $propertyCode, roomCode: $roomCode) {
      id,
      name
    }
  }
`
const ratesVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode,
    "roomCode": payload.roomCode
  }
}

const ratesRequest = (payload) => {
  return {
    query: ratesQuery,
    variables: ratesVariables(payload)
  }
}


//RATE

const rateQuery = `
  query getRate($accountName: String!, $language: String!, $propertyCode: String!, $roomCode: String!, $rateId: Int!) {
    rate(accountName: $accountName, language: $language, propertyCode: $propertyCode, roomCode: $roomCode, rateId: $rateId) {
      id,
      name
    }
  }
`
const rateVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode,
    "roomCode": payload.roomCode,
    "rateId": payload.rateId
  }
}

const rateRequest = (payload) => {
  return {
    query: rateQuery,
    variables: rateVariables(payload)
  }
}

//SERVICES

const servicesQuery = `
  query getServices($accountName: String!, $language: String!, $propertyCode: String!, $roomCode: String!, $rateId: Int!) {
    services(accountName: $accountName, language: $language, propertyCode: $propertyCode, roomCode: $roomCode, rateId: $rateId) {
      id,
      name
    }
  }
`
const servicesVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode,
    "roomCode": payload.roomCode,
    "rateId": payload.rateId
  }
}

const servicesRequest = (payload) => {
  return {
    query: servicesQuery,
    variables: servicesVariables(payload)
  }
}


//SERVICE

const serviceQuery = `
  query getService($accountName: String!, $language: String!, $propertyCode: String!, $roomCode: String!, $rateId: Int!, $serviceId: Int!) {
    service(accountName: $accountName, language: $language, propertyCode: $propertyCode, roomCode: $roomCode, rateId: $rateId, serviceId: $serviceId) {
      id,
      name
    }
  }
`
const serviceVariables = (payload) => {
  return {
    "accountName": payload.accountName,
    "language": payload.language,
    "propertyCode": payload.propertyCode,
    "roomCode": payload.roomCode,
    "rateId": payload.rateId,
    "serviceId": payload.serviceId
  }
}

const serviceRequest = (payload) => {
  return {
    query: serviceQuery,
    variables: serviceVariables(payload)
  }
}

export {
  propertiesRequest,
  propertyRequest,
  roomsRequest,
  roomRequest,
  ratesRequest,
  rateRequest,
  servicesRequest,
  serviceRequest
}
