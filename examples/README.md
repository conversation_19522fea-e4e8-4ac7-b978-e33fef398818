This folder simply contains some code that might be useful
while developing Quotelier.

It currently only contains:


* `sampleOperator.json` example operator definition that you can place on S3, mirrors the graphql schema defined in the graphql service
* `/reduxApp` a sample React+Redux client implementation for reference.
* `/graphQLExpress` and tiny express+graphql app that uses the `@quotelier/graphql` package to run a local graphql server that also hosts GraphiQL and talks to whatever `stage` deployment you set in its `.env` file.

