# Quotelier Development Documentations

This documentation is about the codebase and infrastructure of quotelier-backend (repository). To fully understand the documentation it requires some basic-knowledge listed below.

## Basic knowledge requirements

- HTML, CSS, Javascript
- NodeJS
  - ES5-ES8
  - Notations: import, export, await, async
  - Handling asynchronous errors using promise-chains
  - Packages:
    - JWT
    - GraphQL
    - jsonschema & join (for object validation)
    - chai & mocha & sinon (for unit/e2e testing)
    - aws-sdk (interacting with aws)
- Typescript
  - Type and interface definitions
  - await, import and async functions (ES8)
- GraphQL Protocol and Usage
- OOP and Functional programming
- Git and gitflow
- Terminal familiarity (unix)
- Amazon Web Services
  - Services: Lambda, DynamoDB, Elasticsearch, S3, API Gateway, Cloud Watch, Cloud Formation, StepFunctions
- Serverless Framework 
  - how to deploy (commands)
  - how to define resources into a service
  - how to write functions
  - how to use the cloud-formation resources ( see )
  - how to register plugins
- Bitbucket Pipelines and the CI configuration "bitbucket-pipelines.yml"

## Content

- [Getting Started](./GETTING_STARTED.md)
- Services
  - [Attribute Store](./services/ATTRIBUTE_STORE.md)
  - [GraphQL](./services/GRAPHQL.md)