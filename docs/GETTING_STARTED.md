# Getting started

## Configuration

### System requirements:

- yarn
- node8
- serverless

After you cloned the repository and checked out the development branch, install all the root dependencies using following command:

```
yarn install
```

### AWS Credentials

Make sure you configured your quotelier profile under the location `~/.aws/credentials`:

```
[default]
aws_access_key_id=xxxxx
aws_secret_access_key=xxxxx

[quotelier]
aws_access_key_id=xxxxx
aws_secret_access_key=xxxx
```

the default profile is not relevant. More information about credentials: serverless-credentials

## Prepare services

Each backend service has its own package dependencies, to install all dependencies run the npm script `install-all` script:

```
npm run install-all
```

## Run tests

Most services already provide test-cases, to execute the tests and make sure we are in the right direction, execute following command:

```
cd services/<service to test>

npm run test
```

Info: The ec2 container `localstack` needs to be running run the tests successfully, more info about localstack: github - localstack

### GraphQL Endpoint Tests

Under the directory `test/` you will find the abstract high-level test-cases to test the graphql exposed endpoint functionality. Those test-cases are slow and require an running environment. To run the tests execute the following:

```
cd test
yarn install
npm run test
```

## Deployment

### Prepare environment

following environment definitions are required to deploy all services without issues. Most services need just `STAGE` and `REGION`, use the command-line environment definition instead of exporting to the system environment (security reasons).

```
# SECURITY_GROUP=sg-442dc12c
# SUBNET_PRIVATE=subnet-9d4369f4
# SENTRY_KEY=28662400b54b48ecbac0647d79228fba
# SENTRY_PROJECT=1304565
# STAGE=devel
# REGION=eu-central-1

# Deployment command:
SECURITY_GROUP=sg-442dc12c SUBNET_PRIVATE=subnet-9d4369f4 SENTRY_KEY=28662400b54b48ecbac0647d79228fba SENTRY_PROJECT=1304565 ALARMS_EMAIL_TARGET=<EMAIL> STAGE=devel REGION=eu-central-1 npm run deploy
```

any other  needed environment variable is available from Bitbucket Pipelines CI settings.

### Service deployment

Lets deploy the indexing service, which requires only `STAGE` and `REGION`.

```
STAGE=devel REGION=eu-central-1 npm run deploy
```

## Commands

The repository provides also some commands to perform some manual steps such as dynamodb-backups or exports. To see how you can execute a command read the [README.md](../commands/README.md) OR execute the following command to see all available commands:


```
./commands/run --help

# OUTPUT:usage: run [-h] [-v]

{backup/ddb-dump,backup/ddb-list,backup/ddb-load,export/offers,export/requests,tables/transfer-items}
...

Argparse example

test

Optional arguments:
-h, --help Show this help message and exit.
-v, --version Show program's version number and exit.

available commands:
{backup/ddb-dump,backup/ddb-list,backup/ddb-load,export/offers,export/requests,tables/transfer-items}
```