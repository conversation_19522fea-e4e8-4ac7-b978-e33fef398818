# Attribute Store Service

Location: [graphql](../../services/graphql)

This service is responsible to expose the backend functionality to the `quotelier-react-app` it uses the [GraphQL](https://graphql.org/learn/) protocol.


# Client Authorization

To authorize the client, verify the header [JWT token](https://jwt.io/) in the current format:

```
Authorization: Bearer <JWT token>
```

Here is an example schema of a signed JWT token:

```
{
  "permissions": [
    {
      "ACROTELAR": "assign"
    },
    {
      "ACROTELAR": "own"
    },
    {
      "ACROTELAR": "offer-options"
    }
  ],
  "accountName": "ACROTEL",
  "operatorId": "<EMAIL>",
  "exp": **********
}
```

Interface:

```typescript

enum PERMISSION {
    own
    send
    followup
    expire
    accept
    extend
    confirm
    revert
    cancel
    archive
    unmarkToFollowUp
    acceptUnavailable
    cancelUnavailable
    waitlist
}

interface PermissionItem {

    /**
     * Key (string): The account QProperty.name
     */
    [string]: PERMISSION
}

interface JWTDataSchema {

    /**
     * The property permissions.
     */
    permissions: PermissionItem[]

    /**
     * The account name
     */
    accountName: string
    /**
     * The operation id which is an email.
     */
    operatorId: string
    /**
     * JWT expiration timestamp
     */
    exp: number
}

```

After the jwt verification we check the permission before we perform any query OR mutation.

Those rules are defined in the ACL of the graphql service under the file: [libs/acl/access.js](../../services/graphql/libs/acl/access.js)

We use a custom package [@quotelier/graphql-access-control](../../pakcages/graphql-access-control). This packages allows to decorate/wrap queries AND mutations using following notation:

```javascript
  accessControl.check(
    ['mutation.createRequest'],
    (jwtData, source, args, { acl }) => {

        // ... check logic

        /**
         * Return values:
         * 
         * false = deny access without message
         * true = allow access to the resolver
         * "your are not allowed to do this" = deny access with the message "your are not allowed to do this"
         * 
         * Info: The handler also supports Promises.
         */
        
        return Promise.resolve(true) // allow access (async promise)
        return false; // deny access
        return Promise.resolve("not allowed") // deny with message
    }
  );
```

Inside every handler we provide the `graphq.context` with an [ACL - permissions.js](../../services/graphql/libs/acl/permission.js) instance to provide some abstract and reusable methods.


> The **JWT** is created and signed by the Rails backend of quotelier and bypassed to the react client application which is used to connect to this graphql backend.

# Resolvers

In general resolvers just invoke lambda functions of the internal serverless FaaS architecture using the package [@quotelier/invokeLambda](../../packages/invokeLambda). Some resolvers use the utility function [invokeService OR invokeLambda](../../services/graphql/libs/utils/invokeLambda.js)

# Invocation error handling

We use the npm package [graphql-errors](https://www.npmjs.com/package/graphql-errors) to separate internal errors and user errors. This decision is made at the function [handleCommonErrorResponse](../../services/graphql/libs/utls/invokeLambda.js#L58) by checking the invocation response property `success: true|false`. The difference from `UserError` and `Error` is that we hide the error-tracing and the message from client to avoid system information leaks.