# Attribute Store Service

Location: [attribute-store](../..services/attribute-store)

A service to persist dynamic attributes. We use this service to persist dynamic `QRequest.attributes` (see usage file: [newRequest/lib/customAttributes.js](../../services/newRequest/lib/customAttributes.js))

**Keys**

The attribute store creates multiple records for each defined data property, which means a function call: 

```
store("foo-di", {
    test: 1,
    test2: 2
});
```

will create two dynamodb records:

```
1. {id: "foo-id", name: "test", value: 1}
1. {id: "foo-id", name: "test2", value: 2}
```

while quering the `foo-id` attributes, the function `get` creates an summary object based on those records.

> The reason we are doing this is to store attributes without reaching the item-size limits of dynamodb.
