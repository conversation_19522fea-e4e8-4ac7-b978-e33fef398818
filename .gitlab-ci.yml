stages:
    - test
    - build
    - deploy-service-layer-first
    - deploy-service-layer-second
    - deploy-service-layer-third
    - deploy-service-layer-last

image: node:16

before_script:
    - yarn global add install serverless@1.83.3 --ignore-engines

.deploy:service:
    stage: deploy
    only:
        refs:
            - devel
            - staging
            - master
    variables:
        RELEASE: ${CI_COMMIT_BRANCH}-${CI_COMMIT_REF_NAME}
    script:
        - sls config credentials --provider aws --profile quotelier --key $AWS_ACCESS_KEY_ID --secret $AWS_SECRET_ACCESS_KEY
        - . ./scripts/ci/export_dynamic_vars
        - cd services/$SERVICE/
        - yarn install --pure-lockfile
        - npm run deploy

.test:service:
    stage: test
    script:
        - cd services/$SERVICE/
        - yarn install --pure-lockfile
        - yarn test

.test:package:
    stage: test
    script:
        - cd packages/$PACKAGE/
        - yarn install --pure-lockfile
        - yarn test

test:package:handlebars:
    extends: .test:package
    variables:
        PACKAGE: handlebars
    only:
        changes:
            - packages/handlebars

test:service:graphql:
    extends: .test:service
    variables:
        SERVICE: graphql
    only:
        changes:
            - packages/contracts/**/*
            - packages/graphql-access-control/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/graphql/**/*

test:service:indexing:
    extends: .test:service
    variables:
        SERVICE: indexing
    only:
        changes:
            - services/indexing/**/*

test:service:integrations:
    extends: .test:service
    variables:
        SERVICE: integrations
    only:
        changes:
            - packages/contracts/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/integrations/**/*

test:service:newRequest:
    image: node:16
    extends: .test:service
    variables:
        SERVICE: newRequest
    only:
        changes:
            - packages/handlebars/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/newRequest/**/*

test:service:notifications:
    extends: .test:service
    variables:
        SERVICE: notifications
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/notifications/**/*

test:service:operators:
    extends: .test:service
    variables:
        SERVICE: operators
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/operators/**/*

test:service:personal-data:
    extends: .test:service
    variables:
        SERVICE: personal-data
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/quotelier-types/**/*
            - services/personal-data/**/*

test:service:properties:
    extends: .test:service
    variables:
        SERVICE: properties
    only:
        changes:
            - packages/logger/**/*
            - services/properties/**/*

test:service:scheduler:
    extends: .test:service
    variables:
        SERVICE: scheduler
    only:
        changes:
            - packages/logger/**/*
            - packages/invokeLambda/**/*
            - services/scheduler/**/*

test:service:upsales:
    extends: .test:service
    variables:
        SERVICE: upsales
    only:
        changes:
            - packages/handlebars/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/upsales/**/*

test:service:workflows:
    extends: .test:service
    variables:
        SERVICE: workflows
    only:
        changes:
            - packages/logger/**/*
            - packages/invokeLambda/**/*
            - packages/quotelier-types/**/*
            - services/workflows/**/*

deploy:service:attribute-store:
    extends: .deploy:service
    variables:
        SERVICE: attribute-store
    only:
        changes:
            - services/attribute-store/**/*

deploy:service:graphql:
    extends: .deploy:service
    stage: deploy-service-layer-last
    variables:
        SERVICE: graphql
    only:
        changes:
            - packages/contracts/**/*
            - packages/graphql-access-control/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/graphql/**/*

deploy:service:attribute-store:
    extends: .deploy:service
    stage: deploy-service-layer-first
    variables:
        SERVICE: attribute-store
    only:
        changes:
            - services/attribute-store/**/*

deploy:service:indexing:
    extends: .deploy:service
    stage: deploy-service-layer-first
    variables:
        SERVICE: indexing
    only:
        changes:
            - packages/logger/**/*
            - services/indexing/**/*

deploy:service:integrations:
    extends: .deploy:service
    stage: deploy-service-layer-first
    variables:
        SERVICE: integrations
    only:
        changes:
            - packages/contracts/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/integrations/**/*

deploy:service:newRequest:
    image: node:16
    extends: .deploy:service
    stage: deploy-service-layer-third
    variables:
        SERVICE: newRequest
    only:
        changes:
            - packages/handlebars/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/newRequest/**/*

deploy:service:notifications:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: notifications
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/notifications/**/*

deploy:service:operators:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: operators
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/operators/**/*

deploy:service:personal-data:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: personal-data
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/quotelier-types/**/*
            - services/personal-data/**/*

deploy:service:properties:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: properties
    only:
        changes:
            - packages/logger/**/*
            - services/properties/**/*

deploy:service:insights:
    extends: .deploy:service
    stage: deploy-service-layer-third
    variables:
        SERVICE: insights
    only:
        changes:
            - packages/logger/**/*
            - services/insights/**/*

deploy:service:proposals:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: proposals
    only:
        changes:
            - packages/handlebars/**/*
            - services/proposals/**/*
    script:
        # shares files with the newRequest service and requires the dependencies
        - sls config credentials --provider aws --profile quotelier --key $AWS_ACCESS_KEY_ID --secret $AWS_SECRET_ACCESS_KEY
        - . ./scripts/ci/export_dynamic_vars
        - cd services/newRequest
        - yarn install --pure-lockfile
        - cd ../$SERVICE/
        - yarn install --pure-lockfile
        - npm run deploy

deploy:service:render:
    extends: .deploy:service
    stage: deploy-service-layer-first
    variables:
        SERVICE: render
    only:
        changes:
            - packages/handlebars/**/*
            - packages/invokeLambda/**/*
            - packages/quotelier-types/**/*
            - services/render/**/*

deploy:service:scheduler:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: scheduler
    only:
        changes:
            - packages/logger/**/*
            - packages/invokeLambda/**/*
            - services/scheduler/**/*

deploy:service:upgrade:
    extends: .deploy:service
    stage: deploy-service-layer-third
    variables:
        SERVICE: upgrade
    only:
        changes:
            - packages/invokeLambda/**/*
            - packages/quotelier-types/**/*
            - services/upgrade/**/*

deploy:service:upsales:
    extends: .deploy:service
    stage: deploy-service-layer-third
    variables:
        SERVICE: upsales
    only:
        changes:
            - packages/handlebars/**/*
            - packages/invokeLambda/**/*
            - packages/logger/**/*
            - services/upsales/**/*

deploy:service:upsales-proposals:
    extends: .deploy:service
    stage: deploy-service-layer-second
    variables:
        SERVICE: upsales-proposals
    only:
        changes:
            - services/upsales-proposals/**/*

deploy:service:workflows:
    extends: .deploy:service
    stage: deploy-service-layer-last
    variables:
        SERVICE: workflows
    only:
        changes:
            - packages/logger/**/*
            - packages/invokeLambda/**/*
            - packages/quotelier-types/**/*
            - services/workflows/**/*
