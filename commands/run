#!/usr/bin/env node

const { SharedIniFileCredentials, config } = require('aws-sdk');
const { ArgumentParser, RawTextHelpFormatter } = require('argparse');
const { resolve } = require('path');
const assert = require('assert');
const glob = require('glob');

const { CI } = process.env;

if (CI !== 'true') {
  /**
   * Choose quotelier credentials profile
   */
  var credentials = new SharedIniFileCredentials({ profile: 'quotelier' });
  config.credentials = credentials;
} else {
  /**
   * Use environment credential definitions
   */
}

const parser = new ArgumentParser({
  version: '0.0.1',
  addHelp: true,
  description: 'Argparse example\n\n test',
  formatterClass: RawTextHelpFormatter,
});

let availableCommands = glob.sync(`${__dirname}/**/*.command.js`).map(filename => {
  const name = filename
    .replace(`${__dirname}/`, '')
    .replace('.command.js', '')
    .replace('commands/', '');
  const filepath = resolve(`${filename}`);
  const mod = require(filepath);
  assert(mod.action, `command module ${filepath} does not provide action handler`);
  return { module: mod, filepath, name };
});

const subparsers = parser.addSubparsers({
  title: 'available commands',
  formatterClass: RawTextHelpFormatter,
});

let commands = {};

availableCommands.forEach(command => {
  commands[command.name] = command;
  const subparse = subparsers.addParser(command.name, {
    description: command.module.description,
    formatterClass: RawTextHelpFormatter,
  });
  subparse.addArgument('--action', { defaultValue: command.name });
  command.module.args = command.module.args || [];
  command.module.args.forEach(arg => {
    subparse.addArgument(arg.arguments, arg.options);
  });
});

const args = parser.parseArgs();

assert(args.action, 'No action defined');
let command = commands[args.action];
console.time(command.name);
Promise.resolve()
  .then(() => {
    return command.module.action(JSON.parse(JSON.stringify(args)));
  })
  .then(response => {
    if (response) {
      console.log('Action done with results: ', JSON.stringify(response));
    }
    console.timeEnd(command.name);
    return process.exit(0);
  })
  .catch(err => {
    console.error(err.message);
    console.timeEnd(command.name);
    return process.exit(1);
  });
