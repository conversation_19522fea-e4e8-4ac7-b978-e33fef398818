const { DynamoDB } = require('aws-sdk');
const Promise = require('bluebird');
const chunk = require('chunk');

const DESCRIPTION = `

`;

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--from'],
      options: { required: true },
    },
    {
      arguments: ['--to'],
      options: { required: true },
    },
  ],
  description: DESCRIPTION,
  action: ({ region, from, to }) => {
    const doc = new DynamoDB.DocumentClient({ region });
    return dynamoDBStreamBatchHandler({
      doc,
      tableName: from,
      handler: items => {
        console.log(`Receive items: ${items.length}`);
        const chunks = chunk(items, 25);
        return Promise.map(
          chunks,
          items => {
            console.log(`Load items: ${items.length}`);
            return loadBatch(doc, items, to);
          },
          { concurrency: 20 }
        );
      },
    });
  },
};

/**
 * @param {DocumentClient} doc
 * @param {string} tableName
 * @param {object} [lastEvaluatedKey]
 * @param {function} document<PERSON><PERSON>ler
 * @param {string} stage
 *
 * @returns {Promise<void>}
 */
function dynamoDBStreamBatchHandler({ doc, tableName, lastEvaluatedKey = null, handler }) {
  return doc
    .scan({ TableName: tableName, ExclusiveStartKey: lastEvaluatedKey, Limit: 500 })
    .promise()
    .then(({ Items, LastEvaluatedKey }) => {
      return Promise.resolve()
        .then(() => handler(Items))
        .then(() => {
          if (LastEvaluatedKey) {
            return dynamoDBStreamBatchHandler({
              doc,
              tableName,
              lastEvaluatedKey: LastEvaluatedKey,
              handler,
            });
          } else {
            return true;
          }
        });
    });
}

/**
 * @param {DocumentClient} doc
 * @param {Array<Object>} records
 * @param {string} tableName
 * @returns {Promise<any>}
 */
function loadBatch(doc, records, tableName) {
  const requestItems = {};
  requestItems[tableName] = records.map(record => {
    return {
      PutRequest: {
        Item: record,
      },
    };
  });

  return doc.batchWrite({ RequestItems: requestItems }).promise();
}
