const { DynamoDB } = require('aws-sdk');
const Promise = require('bluebird');
const json2csv = require('json2csv').parse;
const moment = require('moment');

const DESCRIPTION = `

`;

const OPTIONAL_FIELDS = {
  contact: {
    map: (item) => {
      return item["contact"]["email"];
    }
  },
  country: {
    map: (item) => {
      return item["contact"]["country"];
    }
  },
  channel: {
    map: (item) => {
      return item["request"]["channel"];
    }
  },
  category: {
    map: (item) => {
      return (item["request"]["tags"] || []).join(",");
    }
  },
  updatedAt: {
    map: (item) => {
      return moment(parseInt(item["updatedAt"] || item['activities']["updated"])).format('DD-MM-YYYY HH:mm:ss')
    }
  }
}

const OPTIONAL_FILTERS = {
  accountName: {
    setup: (params, filterValue) => {
      params["FilterExpression"] = "#request.accountName=:accountNameValue";
      params["ExpressionAttributeValues"] = { ":accountNameValue": filterValue };
      params["ExpressionAttributeNames"] = { "#request": "request" };
      return params;
    }
  },
  hasError: {
    setup: (params) => {
      params["FilterExpression"] = "attribute_exists(errors)"
      return params;
    }
  }
}

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--from'],
      options: { required: true },
    },
    {
      arguments: ['--fields'],
      options: { required: false },
    },
    {
      arguments: ['--filterExpression'],
      options: { required: false },
    },
  ],
  description: DESCRIPTION,
  action: ({ region, from, to, fields, filterExpression }) => {
    let first = true;
    const doc = new DynamoDB.DocumentClient({ region });
    const optionalFields = (fields || "").split(",").map(field => field.trim()).filter(field => !!field);

    const filters = (filterExpression || "").split(",").map(filter => filter.trim()).filter(filter => !!filter);

    return dynamoDBStreamBatchHandler({
      filters,
      doc,
      tableName: from,
      handler: items => {
        const data = items.map(item => mapRecordToRowData(item, optionalFields || []));

        if(data.length === 0) {
          return;
        }

        const csv = json2csv(data, {
          header: first,
          fields: getFieldHeaders([
            "id",
            "accountName",
            "propertyCode",
            "created",
            "operator",
            "state",
          ], optionalFields || [])
        });
        first = false;
        console.log(csv);
      },
    });
  },
};

function mapRecordToRowData(item, optionalFields) {
  const initialData = {
    id: item.id,
    accountName: item.request.accountName,
    propertyCode: item.request.propertyCode,
    created: moment(parseInt(item["createdAt"] || item['activities']["created"])).format(
      'DD-MM-YYYY HH:mm:ss'
    ),
    contact: item['contact']['email'],
    operator: item['operatorId'],
    state: item.state
  };

  return optionalFields.reduce((data, fieldName) => {
    if (fieldName && OPTIONAL_FIELDS[fieldName] === undefined) {
      throw new Error(`Field: ${fieldName} can not be mapped.`);
    }
    data[fieldName] = OPTIONAL_FIELDS[fieldName].map(item);
    return data;
  }, initialData);
}

function getFieldHeaders(initialFields, optionalFields) {

  return [].concat(initialFields, optionalFields);
}

function setupOptionalFilters(params, filters) {

  return filters.reduce((params, filter) => {
    const [name, value] = filter.split("=");

    if (OPTIONAL_FILTERS[name] === undefined) {
      throw new Error(`Filter name: ${name} can not be mapped.`);
    }
    return OPTIONAL_FILTERS[name].setup(params, value);
  }, params);
}

/**
 * @param {DocumentClient} doc
 * @param {string} tableName
 * @param {object} [lastEvaluatedKey]
 * @param {function} documentHandler
 * @param {string} stage
 *
 * @returns {Promise<void>}
 */
function dynamoDBStreamBatchHandler({ filters, doc, tableName, lastEvaluatedKey = null, handler }) {

  const params = setupOptionalFilters({
    TableName: tableName,
    ExclusiveStartKey: lastEvaluatedKey,
    Limit: 500,
  }, filters);

  return doc
    .scan(params)
    .promise()
    .then(({ Items, LastEvaluatedKey }) => {

      return Promise.resolve()
        .then(() => handler(Items))
        .then(() => {
          if (LastEvaluatedKey) {
            return dynamoDBStreamBatchHandler({
              doc,
              tableName,
              lastEvaluatedKey: LastEvaluatedKey,
              handler,
              filters,
            });
          } else {
            return true;
          }
        });
    });
}

