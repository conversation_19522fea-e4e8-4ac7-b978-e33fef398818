# Commands

Commands are actions or processes related to the project quotelier, which are executed manually using the commands-line. This folder contains als the command runner "run" file who is responsible to run and display the available commands.

To display the available commands you have to run:

```bash
./commands/run -h
```
 you terminal should display something similar to this:
 
 ```text
 usage: run [-h] [-v] {migrate-accounts} ...

Argparse example

Optional arguments:
  -h, --help          Show this help message and exit.
  -v, --version       Show program's version number and exit.

available commands:
  {migrate-accounts}
 ```

You can also display more detailed information about the commands.

```bash
./commands/run migrate-accounts -h
```

Output should look like the following:

```text
usage: run migrate-accounts [-h] [--action ACTION]

Migrate old accounts from account table to the integrations service using the 
createAccount lambda function

Optional arguments:
  -h, --help       Show this help message and exit.
  --action ACTION
```

## Create an new command

To create a command you have to create a new js file inside the `commands/` folder with the suffix ".command.js" , this file will represent you command.
The prefix of the file will be the command name, such as "hello-world" => "hello-world.command.js".

The runner expects a specific export interface of every command file, see below:

```javascript
module.exports = {action: () => {console.log('works')}}
```
This is the minimum required interface for the runner to perform the action, other optional attributes are also available:

- args: Define the arguments of the main arg parser: [More info](https://github.com/nodeca/argparse#example)

Example:
```text
  args (Array<ArgumentDefinitions>): [
    {
      arguments: ['--stage'],
      options: { required: true },
    },
    {
      arguments: ['--region'],
      options: { required: true },
    },
  ]
```
- description (string): You command description, which we display when the user whats more information about the command.
