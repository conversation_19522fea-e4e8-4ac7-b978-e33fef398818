const { S3, DynamoDB } = require('aws-sdk');
const fs = require('fs');
const Promise = require('bluebird');
const { storeDump } = require('./lib');

const DESCRIPTION = `
  DynamoDB backup to an S3 destination.

  Backup destination location: "<to:backetname>/dynamodb_dumps/<from:tablename>/<timestamp>".

  This command performs a dynamodb scan and stores it temporary at "/tmp/dynamodb-backup-<PID>".
  Once all data are collected, the temporary data are uploaded via stream-upload to the S3 destination.

  The backup process is rebust and scalable as long the local-host has enough space.
`;

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--table-name'],
      options: { required: true, help: 'DynamoDB name as backup data-source.' },
    },
    {
      arguments: ['--storage'],
      options: {
        required: true,
        help: 'S3 bucket-name destination as the backup storage.',
      },
    },
  ],
  description: DESCRIPTION,
  action: ({ region, table_name, storage }) => {
    const doc = new DynamoDB.DocumentClient({ region });
    const s3 = new S3({ region });
    const backupFile = '/tmp/dynamodb-backup-' + process.pid;
    return dynamoDBStreamBatchHandler({
      doc,
      tableName: table_name,
      handler: items => {
        console.log('Receive ' + items.length, 'documents');
        items.forEach(item => fs.appendFileSync(backupFile, JSON.stringify(item) + '\n\n'));
      },
    }).then(() => {
      return storeDump(s3, storage, table_name, backupFile).then(() => 'backup successfull');
    });
  },
};

/**
 * @param {DocumentClient} doc
 * @param {string} tableName
 * @param {object} [lastEvaluatedKey]
 * @param {function} documentHandler
 * @param {string} stage
 *
 * @returns {Promise<void>}
 */
function dynamoDBStreamBatchHandler({ doc, tableName, lastEvaluatedKey = null, handler }) {
  return doc
    .scan({ TableName: tableName, ExclusiveStartKey: lastEvaluatedKey })
    .promise()
    .then(({ Items, LastEvaluatedKey }) => {
      return Promise.resolve()
        .then(() => handler(Items))
        .then(() => {
          if (LastEvaluatedKey) {
            return dynamoDBStreamBatchHandler({
              doc,
              tableName,
              lastEvaluatedKey: LastEvaluatedKey,
              handler,
            });
          } else {
            return true;
          }
        });
    });
}
