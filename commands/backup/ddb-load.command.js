const { S3, DynamoDB } = require('aws-sdk');
const Promise = require('bluebird');
const path = require('path');
const LineByLineReader = require('line-by-line');
const { getDump } = require('./lib');
const DESCRIPTION = `
  Load data from an dynamoDB dump/backup to a dynamodb destination.
`;

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--table-name'],
      options: { required: true, help: 'DynamoDB table-name destination' },
    },
    {
      arguments: ['--dump-id'],
      options: { required: false, help: 'The dump timestamp id' },
    },
    {
      arguments: ['--storage'],
      options: {
        required: true,
        help: 'S3 bucket-name destination as the backup storage.',
      },
    },
  ],
  description: DESCRIPTION,
  action: ({ region, table_name, storage, dump_id }) => {
    const doc = new DynamoDB.DocumentClient({ region });
    const s3 = new S3({ region });
    const dumpTemp = '/tmp/dynamodb-dump-' + process.pid;

    let getDumpIdPromise = null;

    if (dump_id) {
      getDumpIdPromise = Promise.resolve(dump_id);
    } else {
      getDumpIdPromise = getLatestDump(s3, storage, table_name);
    }

    return getDumpIdPromise
      .then(dumpId => {
        if (!dumpId) {
          throw new Error('No dump found!');
        }
        dump_id = dumpId;
        return getDump(s3, storage, table_name, dump_id);
      })
      .then(() => loadBackupFile(doc, `${dumpTemp}/${dump_id}`, table_name));
  },
};

/**
 * @param {S3} s3
 * @param {string} storage
 * @param {string} table_name
 * @returns {string}
 */
function getLatestDump(s3, storage, table_name) {
  return s3
    .listObjects({
      Bucket: storage,
      Prefix: `dynamodb_dumps/${table_name}`,
    })
    .promise()
    .then(({ Contents }) => {
      return Contents.sort((a, b) => {
        return parseInt(path.basename(a.Key)) > path.basename(b.Key) ? -1 : 1;
      }).map(content => {
        return path.basename(content.Key);
      })[0];
    })
    .then(dumpId => {
      console.log('Found latest dump id: ', dumpId);
      return dumpId;
    });
}

/**
 * @param {DocumentClient} doc
 * @param {string} filepath
 * @param {string} table_name
 * @returns {Promise<void>}
 */
function loadBackupFile(doc, filepath, table_name) {
  return new Promise((resolve, reject) => {
    let recordBatch = [];
    let sumCount = 0;
    const rl = new LineByLineReader(filepath);

    /**
     * @returns {void}
     */
    function loader() {
      sumCount += recordBatch.length;
      console.log('Loaded items', sumCount);
      rl.pause();
      return loadBatch(doc, recordBatch, table_name)
        .then(() => {
          recordBatch = [];
          return rl.resume();
        })
        .catch(err => {
          reject(err);
          return rl.close();
        });
    }

    rl
      .on('line', line => {
        line = line.trim();
        if (!line) {
          return;
        }
        recordBatch.push(JSON.parse(line));
        if (recordBatch.length === 25) {
          loader();
        }
      })
      .on('end', () => {
        loader();
        resolve(null);
      });
  });
}

/**
 * @param {DocumentClient} doc
 * @param {Array<Object>} records
 * @param {string} tableName
 * @returns {Promise<any>}
 */
function loadBatch(doc, records, tableName) {
  const requestItems = {};
  requestItems[tableName] = records.map(record => {
    return {
      PutRequest: {
        Item: record,
      },
    };
  });

  return doc.batchWrite({ RequestItems: requestItems }).promise();
}
