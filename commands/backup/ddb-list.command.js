const { S3 } = require('aws-sdk');
const path = require('path');
const moment = require('moment');

const DESCRIPTION = `
  List all available dynamodb backups of specific tablename

  Backup lookup: "<source:backetname>/dynamodb_dumps/<table-name>/*".
`;

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--storage'],
      options: {
        required: true,
        help: 'S3 bucket-name as the backup storage.',
      },
    },
    {
      arguments: ['--table-name'],
      options: {
        required: true,
        help: 'DynamoDB table-name',
      },
    },
  ],
  description: DESCRIPTION,
  action: ({ region, storage, table_name }) => {
    const s3 = new S3({ region });

    return s3
      .listObjects({
        Bucket: storage,
        Prefix: `dynamodb_dumps/${table_name}`,
      })
      .promise()
      .then(({ Contents }) => printAvailableDumps(Contents));
  },
};

/**
 * @param {Array<Object>} s3Contents
 * @returns {void}
 */
function printAvailableDumps(s3Contents) {
  console.log('--------------------------------');
  s3Contents
    .sort((a, b) => {
      return parseInt(path.basename(a.Key)) > path.basename(b.Key) ? -1 : 1;
    })
    .forEach(({ Key, LastModified }) => {
      console.log(
        `ID: ${path.basename(Key)} ( ${moment(LastModified).toISOString()} - ${moment(
          LastModified
        ).fromNow()} )`
      );
    });
  console.log('--------------------------------');
  return null;
}
