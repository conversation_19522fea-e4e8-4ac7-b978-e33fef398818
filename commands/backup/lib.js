const fs = require('fs');
const crypto = require('crypto');
var zlib = require('zlib');

const ALGORITHM = 'aes-256-ctr';

const { BACKUP_SECRET } = process.env;

const zip = zlib.createGzip();
const unzip = zlib.createGunzip();

/**
 * @param {S3} s3
 * @param {string} storage
 * @param {string} tableName
 * @param {string} filepath
 * @returns {Promise<void>}
 */
function storeDump(s3, storage, tableName, filepath) {
  const encrypt = crypto.createCipher(ALGORITHM, BACKUP_SECRET);

  const readStream = fs
    .createReadStream(filepath)
    .pipe(zip)
    .pipe(encrypt);

  return s3
    .upload({
      Bucket: storage,
      Key: ['dynamodb_dumps', tableName, Date.now().toString()].join('/'),
      Body: readStream,
    })
    .promise();
}

/**
 * @param {S3} s3
 * @param {string} storage
 * @param {string} tableName
 * @param {string} dumpId
 * @returns {Promise<File>}
 */
function getDump(s3, storage, tableName, dumpId) {
  const decrypt = crypto.createDecipher(ALGORITHM, BACKUP_SECRET);

  return new Promise((resolve, reject) => {
    const dumpTemp = '/tmp/dynamodb-dump-' + process.pid;
    fs.mkdirSync(dumpTemp);
    const targetFile = fs.createWriteStream(`${dumpTemp}/${dumpId}`);
    return s3
      .getObject({
        Bucket: storage,
        Key: `dynamodb_dumps/${tableName}/${dumpId}`,
      })
      .createReadStream()
      .on('end', () => {
        return resolve(targetFile);
      })
      .on('error', err => reject(err))
      .pipe(decrypt)
      .pipe(unzip)
      .pipe(targetFile);
  });
}

module.exports = { storeDump, getDump };
