/**
 * Command to export data from elasticsearch index.
 *
 * Command example:
 *
 * ```
 * ./commands/run  export/requests --stage quote --region eu-central-1 --fields "id, request.accountName, request.propertyCode, activities.created, state" --query "*"
 * ```
 */

const AWS = require('aws-sdk');
const elasticsearch = require('elasticsearch');
const connectionClass = require('http-aws-es');
const json2csv = require('json2csv').parse;
const moment = require('moment');
const fs = require('fs');

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--stage'],
      options: { required: true },
    },
    {
      arguments: ['--query'],
      options: { required: false },
    },
    {
      arguments: ['--fields'],
      options: { defaultValue: 'id, operatorId' },
    },
    {
      arguments: ['--created'],
      options: { defaultValue: null },
    },
  ],
  description: 'Export requests as CSV',
  action: action,
};

/**
 * @param {string} region
 * @param {string} stage
 *
 * @returns {Promise<any>}
 */
function action({ region, stage, fields, query, created }) {
  AWS.config.update({ region });
  let connection = null;
  const from = created ? moment(created, 'DD-MM-YYYY') : null;

  fields = fields.split(',').map(field => field.trim());
  return getStageElasticSearchEndpoint({ region, stage })
    .then(endpoint => connectES(endpoint))
    .then(con => (connection = con))
    .then(() => {
      let queryObject = {};
      if (from) {
        queryObject = {
          range: {
            'activities.created': {
              gte: from.toDate().getTime(),
            },
          },
        };
      } else {
        queryObject = {
          query_string: {
            query: query,
          },
        };
      }
      return search(connection, {
        index: `${stage}_newrequests`,
        type: 'request',
        size: 10000,
        scroll: '1m',
        _source: fields,
        body: { query: queryObject },
      });
    })
    .then(results => {
      return results.map(hit => {
        return hit._source;
      });
    })
    .then(data => {
      if (!data.length) {
        return 'No data received';
      }
      const exportName = `${from ? from.format('DD-MM-YYYY') + '_' : ''}${moment().format(
        'DD-MM-YYYY'
      )}`;
      data = data.map(row => {
        if (row['activities']) {
          /**
           * Transform timestamp to iso date
           */
          Object.keys(row['activities']).forEach(activityName => {
            row['activities'][activityName] = moment(row['activities'][activityName]).format(
              'DD-MM-YYYY HH:mm:ss'
            );
          });
        }
        return row;
      });
      fs.writeFileSync(`./requests-export-${exportName}.csv`, json2csv(data, { fields }), {
        encoding: 'utf-8',
      });
      return `Export file created: ./requests-export-${exportName}.csv`;
    });
}

/**
 * Gets all search hits using the scroll functionality.
 *
 * @param {any} connection
 * @param {object} params
 * @returns {Array<object>}
 */
function search(connection, params) {
  let received = 0;
  let total = null;
  let results = [];

  /**
   *
   * @param {object} params
   * @param {boolean} scroll
   * @return {Array<object>}
   */
  function get(params, scroll = false) {
    let task;

    if (scroll === false) {
      task = connection.search(params);
    } else {
      console.log(params);
      task = connection.scroll({ scrollId: params.scrollId, scroll: '1m' });
    }

    return task.then(res => {
      const { hits: hitsResult, _scroll_id } = res;
      const { total: totalCount, hits } = hitsResult;

      if (total === null) {
        total = totalCount;
      }

      received += hits.length;
      results = results.concat(hits);

      if (received < total) {
        return get({ scrollId: _scroll_id }, true);
      }

      return results;
    });
  }

  return get(params);
}

/**
 * @param {string} region
 * @param {string} stage
 * @returns {Promise<string>}
 */
function getStageElasticSearchEndpoint({ region, stage }) {
  const es = new AWS.ES({ region });
  return es
    .describeElasticsearchDomain({
      DomainName: `elasticsearch-${stage}-indexing`,
    })
    .promise()
    .then(res => res.DomainStatus.Endpoint)
    .catch(err => {
      if (err.code === 'ResourceNotFoundException') {
        return getExport(new AWS.CloudFormation({ region }), 'common-indexing-endpoint');
      } else {
        throw err;
      }
    });
}

/**
 * @param {AWS.CloudFormation} cf
 * @param {string} exportName
 * @returns {Promise<string>}
 */
function getExport(cf, exportName) {
  return cf
    .listExports()
    .promise()
    .then(({ Exports }) => {
      return Exports.filter(exp => {
        return exp.Name === exportName;
      })[0];
    })
    .then(exp => (exp ? exp.Value : null));
}

/**
 * @param {string} endpoint
 * @returns {Elasticsearch.Client}
 */
function connectES(endpoint) {
  return new elasticsearch.Client({
    host: endpoint,
    connectionClass: connectionClass, // Use this to verify aws access
  });
}
