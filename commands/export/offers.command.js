/**
 * Command to export data from elasticsearch index.
 *
 * Command example:
 *
 * ```
 * ./commands/run  export/requests --stage quote --region eu-central-1 --fields "id, request.accountName, request.propertyCode, activities.created, state" --query "*"
 * ```
 */

const AWS = require('aws-sdk');
const elasticsearch = require('elasticsearch');
const connectionClass = require('http-aws-es');
const json2csv = require('json2csv').parse;
const moment = require('moment');
const fs = require('fs');

module.exports = {
  args: [
    {
      arguments: ['--region'],
      options: { required: true },
    },
    {
      arguments: ['--stage'],
      options: { required: true },
    },
    {
      arguments: ['--query'],
      options: { required: true },
    },
    {
      arguments: ['--fields'],
      options: { defaultValue: 'id, operatorId' },
    },
  ],
  description: 'Export requests as CSV',
  action: action,
};

/**
 * @param {string} region
 * @param {string} stage
 *
 * @returns {Promise<any>}
 */
function action({ region, stage, fields, query }) {
  AWS.config.update({ region });
  let connection = null;
  fields = fields.split(',').map(field => field.trim());
  return getStageElasticSearchEndpoint({ region, stage })
    .then(endpoint => connectES(endpoint))
    .then(con => (connection = con))
    .then(() => {
      return connection.search({
        index: `${stage}_newrequests`,
        type: 'request',
        size: 10000,
        _source: fields,
        body: {
          query: {
            query_string: {
              query: query,
            },
          },
        },
      });
    })
    .then(res => {
      return res.hits.hits.map(hit => {
        return hit._source;
      });
    })
    .then(data => {
      data = data.reduce((newData, item) => {
        const couple = (item.request.tags || []).indexOf('Couple') !== -1;
        const family = (item.request.tags || []).indexOf('Family') !== -1;

        Object.assign(item, { couple, family });

        item.offers.forEach((offer, index) => {
          Object.assign(offer, { id: index });
          newData.push(Object.assign({}, item, { offers: offer }));
        });

        return newData;
      }, []);

      console.log(JSON.stringify(data, null, 4));
      if (!data.length) {
        return 'No data received';
      }
      const exportName = `${moment().format('DD-MM-YYYY')}`;
      data = data.map(row => {
        if (row['activities']) {
          /**
           * Transform timestamp to iso date
           */
          Object.keys(row['activities']).forEach(activityName => {
            row['activities'][activityName] = moment(row['activities'][activityName]).format(
              'DD-MM-YYYY HH:mm:ss'
            );
          });
        }
        return row;
      });
      console.log(fields);
      fs.writeFileSync(`./offers-export-${exportName}.csv`, json2csv(data, { fields }), {
        encoding: 'utf-8',
      });
      return `Export file created: ./offers-export-${exportName}.csv`;
    });
}

/**
 * @param {string} region
 * @param {string} stage
 * @returns {Promise<string>}
 */
function getStageElasticSearchEndpoint({ region, stage }) {
  const es = new AWS.ES({ region });
  return es
    .describeElasticsearchDomain({
      DomainName: `elasticsearch-${stage}-indexing`,
    })
    .promise()
    .then(res => res.DomainStatus.Endpoint)
    .catch(err => {
      if (err.code === 'ResourceNotFoundException') {
        return getExport(new AWS.CloudFormation({ region }), 'common-indexing-endpoint');
      } else {
        throw err;
      }
    });
}

/**
 * @param {AWS.CloudFormation} cf
 * @param {string} exportName
 * @returns {Promise<string>}
 */
function getExport(cf, exportName) {
  return cf
    .listExports()
    .promise()
    .then(({ Exports }) => {
      return Exports.filter(exp => {
        return exp.Name === exportName;
      })[0];
    })
    .then(exp => (exp ? exp.Value : null));
}

/**
 * @param {string} endpoint
 * @returns {Elasticsearch.Client}
 */
function connectES(endpoint) {
  return new elasticsearch.Client({
    host: endpoint,
    connectionClass: connectionClass, // Use this to verify aws access
  });
}
