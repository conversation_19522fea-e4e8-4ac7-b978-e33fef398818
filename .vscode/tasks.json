{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "Export (DEFAULT)",
            "type": "shell",
            "command": "./commands/run tables/list --region eu-central-1 --from new-request-quote-sse-requests --fields contact > $(date +%Y-%m-%d)-all.csv",
        },
        {
            "label": "Export (DOMESHOTELS)",
            "type": "shell",
            "command": "./commands/run tables/list --region eu-central-1 --from new-request-quote-sse-requests --filterExpression accountName=DOMESHOTELS --fields contact,channel,category,country,updatedAt > $(date +%Y-%m-%d)-DOMESHOTELS.csv",
        },
        {
            "label": "Export (MARBELLA)",
            "type": "shell",
            "command": "./commands/run tables/list --region eu-central-1 --from new-request-quote-sse-requests --filterExpression accountName=MARBELLA --fields contact,channel,category,country,updatedAt > $(date +%Y-%m-%d)-MARBELLA.csv",
        },
        {
            "label": "Export offers (MARBELLA)",
            "type": "shell",
            "command": "./commands/run tables/list-offers --region eu-central-1 --from new-request-quote-sse-requests --filterExpression accountName=MARBELLA > $(date +%Y-%m-%d)-MARBELLA.offers.csv",
        },
        {
            "label": "Export offers (DOMESHOTELS)",
            "type": "shell",
            "command": "./commands/run tables/list-offers --region eu-central-1 --from new-request-quote-sse-requests --filterExpression accountName=DOMESHOTELS > $(date +%Y-%m-%d)-DOMESHOTELS.offers.csv",
        },
        {
            "label": "Export (ALL)",
            "type": "process",
            "dependsOrder": "parallel",
            "dependsOn": [
                "Export (DEFAULT)",
                "Export (DOMESHOTELS)",
                "Export (MARBELLA)",
                "Export offers (MARBELLA)",
                "Export offers (DOMESHOTELS)",
            ]
        }
    ]
}