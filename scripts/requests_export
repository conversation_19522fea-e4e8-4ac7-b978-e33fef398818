#!/bin/sh

cd "$(dirname "$0")"

../commands/run tables/list --region $REGION --from new-request-quote-sse-requests  --fields "contact" > requests-export-all.csv

../commands/run tables/list --region $REGION --from new-request-quote-sse-requests --fields "contact,channel,category,country,updatedAt" --filterExpression "accountName=DOMESHOTELS" > requests-export-DOMESHOTELS.csv
 
../commands/run tables/list-offers --region $REGION --from new-request-quote-sse-requests --filterExpression "accountName=DOMESHOTELS"  > request-offers-export-DOMESHOTELS.csv

../commands/run tables/list --region $REGION --from new-request-quote-sse-requests --fields "contact,channel,category,country,updatedAt" --filterExpression "accountName=MARBELLA" > requests-export-MARBELLA.csv

../commands/run tables/list-offers --region $REGION --from new-request-quote-sse-requests --filterExpression "accountName=MARBELLA"  > request-offers-export-MARBELLA.csv
