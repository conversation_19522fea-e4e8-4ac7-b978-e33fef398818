#!/bin/sh

case "$CI_COMMIT_REF_SLUG" in
  "master")
    export STAGE="quote"
    export MANDRILL_TOKEN="$MANDRILL_PRODUCTION_TOKEN"
    export INTERCOM_SECRET="$INTERCOM_PRODUCTION_SECRET"
    ;;

  "staging")
    export STAGE="stage"
    export MANDRILL_TOKEN="$MANDRILL_PRODUCTION_TOKEN"
    export INTERCOM_SECRET="$INTERCOM_DEV_SECRET"
    ;;
  *)
    export STAGE="devel"
    export MANDRILL_TOKEN="$MANDRILL_TEST_TOKEN"
    export INTERCOM_SECRET="$INTERCOM_DEV_SECRET"
    ;;
esac
