#!/bin/sh

cd "$(dirname "$0")"


echo Backup table: new-request-${STAGE}-requests
../commands/run backup/ddb-load --region $REGION --storage $BACKUP_BUCKET_NAME --table-name new-request-${STAGE}-sse-requests


echo Backup table: integrations-${STAGE}-accounts-properties
../commands/run backup/ddb-load --region $REGION --storage $BACKUP_BUCKET_NAME --table-name integrations-${STAGE}-accounts-properties


echo Backup table: integrations-${STAGE}-adaptor-parameters
../commands/run backup/ddb-load --region $REGION --storage $BACKUP_BUCKET_NAME --table-name integrations-${STAGE}-adaptor-parameters


echo Backup table: operators-service-${STAGE}-tag
../commands/run backup/ddb-load --region $REGION --storage $BACKUP_BUCKET_NAME --table-name operators-service-${STAGE}-tag

echo Backup table: upsales-${STAGE}-upsales
../commands/run backup/ddb-load --region $REGION --storage $BACKUP_BUCKET_NAME --table-name upsales-${STAGE}-upsales
