'use strict';

const createHandlebarsEnvironment = require('./../index');
const expect = require('chai').expect;

describe('Handlebars', () => {
  let handlebars;

  beforeEach(() => {
    handlebars = createHandlebarsEnvironment();
  });

  it('should initialize helpers', () => {
    expect(handlebars.helpers).to.be.an('object');
  });

  it('should contain a helper', () => {
    expect(handlebars.helpers).to.haveOwnProperty('ifLess');
  });
});
