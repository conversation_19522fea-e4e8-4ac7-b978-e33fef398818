const { expect } = require('chai');
const createHandlebarsEnvironment = require('./../index');

describe('Helpers', () => {
  let handlebars;
  beforeEach(() => {
    handlebars = createHandlebarsEnvironment();
  });

  describe('summarySplit', () => {
    it('should split the text', () => {
      const result = handlebars.helpers.summarySplit(
        'Lorem Ipsum is [split] the printing and typesetting industry.',
        '[split]',
        1
      );
      expect(result).to.be.equal('Lorem Ipsum is');
    });

    it('should split multiple times', () => {
      const result = handlebars.helpers.summarySplit(
        'Lorem Ipsum is [split] the printing and [split] typesetting industry.',
        '[split]',
        2
      );
      expect(result).to.be.equal('the printing and');
    });

    it('should split and get first block', () => {
      const result = handlebars.helpers.summarySplit(
        'Lorem Ipsum is [split] the printing and [split] typesetting industry.',
        '[split]',
        'first'
      );
      expect(result).to.be.equal('Lorem Ipsum is');
    });

    it('should split and get last block', () => {
      const result = handlebars.helpers.summarySplit(
        'Lorem Ipsum is [split] the printing and [split] typesetting industry.',
        '[split]',
        'last'
      );
      expect(result).to.be.equal('typesetting industry.');
    });

    it('should handle index out of range', () => {
      const result = handlebars.helpers.summarySplit(
        'Lorem Ipsum is [split] the printing and [split] typesetting industry.',
        '[split]',
        20
      );
      expect(result).to.be.equal(null);
    });

    it('should use the default delimiter and block', () => {
      const result = handlebars.helpers.summarySplit(
        'Lorem Ipsum is <!--summary--> the printing and <!--summary--> typesetting industry.'
      );
      expect(result).to.be.equal('Lorem Ipsum is');
    });
  });

  describe('checkoutDate', () => {
    it('should return offer checkout date', () => {
      const fixture = require('./fixtures/proposalPayload.json');
      const result = handlebars.helpers.checkoutDate(fixture.request.offers[0]);
      expect(result).to.be.equal('2017-03-23');
    });
  });

  describe('schemaConfirmation', () => {
    it('should return reservation confirmation linked-data schema', () => {
      const fixture = require('./fixtures/proposalPayload.json');
      const result = handlebars.helpers.schemaConfirmation(
        fixture.request,
        fixture.request.offers[2]
      );
      expect(result).to.be.equal(
        `\n  <script type="application/ld+json">\n  {\n    "@context": "http://schema.org",\n    "@type": "LodgingReservation",\n    "reservationId": "13495345",\n    "reservationStatus": "http://schema.org/Confirmed",\n    "lodgingUnitDescription": "Deluxe Suite",\n    "underName": {\n      "@type": "Person",\n      "name": "Kostas Katsikas"\n    },\n    "checkinTime": "2017-03-21",\n    "checkoutTime": "2017-03-23"\n  }\n  </script>`
      );
    });
  });

  describe('calcOptionRoomRate', () => {
    it('should calculate the sum total price', () => {
      const fixture = require('./fixtures/proposalPayload.json');
      const result = handlebars.helpers.calcOptionRoomRate(fixture.request.offers);
      expect(result).to.eq(135000);
    });
  });

  describe('calcOptionOfficialRate', () => {
    it('should calculate the sum total price', () => {
      const fixture = require('./fixtures/proposalPayload.json');

      const result = handlebars.helpers.calcOptionOfficialRate(fixture.request.offers);
      expect(result).to.eq(213300);
    });
  });

  describe('calcOptionServicesTotalRate', () => {
    it('should calculate the sum services total price', () => {
      const fixture = JSON.parse(JSON.stringify(require('./fixtures/proposalPayload.json')));
      fixture.request.offers[0].serviceTotalRate = 50;
      fixture.request.offers[2].serviceTotalRate = 30;
      const result = handlebars.helpers.calcOptionServicesTotalRate(fixture.request.offers);
      expect(result).to.eq(80);
    });
  });

  describe('calcOptionTotalRate', () => {
    it('should calculate the sum services total price', () => {
      const fixture = JSON.parse(JSON.stringify(require('./fixtures/proposalPayload.json')));
      fixture.request.offers[0].serviceTotalRate = 50;
      fixture.request.offers[2].serviceTotalRate = 30;
      const result = handlebars.helpers.calcOptionTotalRate(fixture.request.offers);
      expect(result).to.eq(135080);
    });
  });

  describe('calcOptionDiscountRate', () => {
    it('should the discount on total prices summary', () => {
      const fixture = require('./fixtures/proposalPayload.json');

      const result = handlebars.helpers.calcOptionDiscountRate(fixture.request.offers);
      expect(result).to.eq('-36.00');
    });
  });

  describe('calcTotalPrice', () => {
    it('should add the service-price to total', () => {
      const fixture = JSON.parse(JSON.stringify(require('./fixtures/proposalPayload.json')));
      fixture.request.offers[0].serviceTotalRate = 200;
      const result = handlebars.helpers.calcTotalPrice(fixture.request.offers[0]);
      expect(result).to.be.equal(45200); // : 22500 (roomRate) * 2 (nights) + 200 (serviceTotalRate)
    });

    it('should keep total without service-price', () => {
      const fixture = require('./fixtures/proposalPayload.json');
      const result = handlebars.helpers.calcTotalPrice(fixture.request.offers[0]);
      expect(result).to.be.equal(45000); // : 22500 (roomRate) * 2 (nights)
    });
  });

  describe('uniqueOptionOffersAccommodation', () => {
    it('should return a list filtered out duplicate rooms', () => {
      const fixture = require('./fixtures/proposalPayload.json');
      const res = handlebars.helpers.uniqueOptionOffersAccommodation(fixture.request.offers);

      expect(res).to.have.length(1);
    });
  });

  describe('uniqueOptionOffersServices', () => {
    it('should return a list filtered out duplicate services', () => {
      const fixture = require('./fixtures/proposalPayload.json');
      const res = handlebars.helpers.uniqueOptionOffersServices(fixture.request.offers);

      expect(res).to.have.length(1);
    });
  });

  describe('serialize', () => {
    it('should return a list filtered out duplicate services', () => {
      const res = handlebars.helpers.serialize({ test: 1 });

      expect(res).to.be.equal('{"test":1}');
    });
  });

  describe('handshareURL', () => {
    it(`should generate expected URL: quotelier/DEMOQUOTEL-25525597?d=56acc16ed2a7b75e8a84cdc360f4a91c`, () => {
      const partialUrl = handlebars.helpers.handshakeUrl('DEMOQUOTEL', '25525597');
      expect(partialUrl).to.be.equal('quotelier/DEMOQUOTEL-25525597?d=56acc16ed2a7b75e8a84cdc360f4a91c')
    });
  });
});
