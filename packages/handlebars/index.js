const fs = require('fs');
const path = require('path');
const Handlebars = require('handlebars');
const handlebarsHelpers = require('handlebars-helpers');

const helpersDir = path.resolve(__dirname, './lib/');

/**
 * Creates a new handlebars-environment with registered helpers.
 *
 * Info: This is necessary because of the dynamic registration per invocation,
 * the handlebars singleton can not handle multiple invocations at the same runtime.
 *
 * @returns {Handlebars}
 */
function createHandlebarsEnvironment() {
  const handlebars = Handlebars.create();

  // Register helpers repo
  handlebarsHelpers({
    handlebars: handlebars,
  });

  // Register our custom helpers
  fs.readdirSync(helpersDir).forEach(file => {
    handlebars.registerHelper(file.replace('.js', ''), require(`${helpersDir}/${file}`));
  });

  return handlebars;
}

module.exports = createHandlebarsEnvironment;
