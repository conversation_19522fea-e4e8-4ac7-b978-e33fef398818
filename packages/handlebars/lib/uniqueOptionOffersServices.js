const _ = require('lodash');

/**
 * Filter out duplicate descriptions of services
 *
 * @param {QOffer[]} offers
 * @returns {QService[]}
 */
module.exports = function uniqueOptionOffersServices(offers) {
  const descriptions = offers.reduce((descriptions, current) => {
    const subDescriptions =
      current.services && current.services.length ? current.services.map(service => service) : [];
    return descriptions.concat(subDescriptions);
  }, []);

  return _.uniqWith(descriptions, _.isEqual);
};
