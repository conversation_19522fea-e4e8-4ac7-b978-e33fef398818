const moment = require('moment');

/**
 * @param {string|number} date
 * @param {string} format
 * @param {string} [language]
 * @returns {string}
 */
module.exports = function formatDate(date, format, language) {
  let momentDate = moment.parseZone(isNaN(date) ? date : parseInt(date));
  if (typeof language === 'string') {
    // skip handlebars passed arguments
    momentDate = momentDate.locale(language);
  }
  return momentDate.format(format);
};
