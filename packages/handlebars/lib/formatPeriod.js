const moment = require('moment');

/**
 * @param {QOffer} offer
 * @param {string} language
 * @param {array} formats
 * @returns {string}
 */
module.exports = (offer, language, formats) => {
  formats = formats || [];
  let checkinDate = moment(offer.checkin)
    .locale(language)
    .format(formats[0] || 'DD MMMM');
  let checkoutDate = moment(offer.checkin)
    .locale(language)
    .add(offer.nights, 'days')
    .format(formats[1] || 'DD MMMM YYYY');
  return `${checkinDate} - ${checkoutDate}`;
};
