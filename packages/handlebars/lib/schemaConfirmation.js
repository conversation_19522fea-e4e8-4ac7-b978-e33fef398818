const moment = require('moment');

/**
 * @param {QRequest} request
 * @param {QOffer} offer
 * @returns {string}
 */
module.exports = (request, offer) => {
  let checkin = moment(offer.checkin).format('YYYY-MM-DD');
  let checkout = moment(offer.checkin)
    .add(offer.nights, 'days')
    .format('YYYY-MM-DD');
  const schemaConfirmation = `
  <script type="application/ld+json">
  {
    "@context": "http://schema.org",
    "@type": "LodgingReservation",
    "reservationId": "${offer.reservationId}",
    "reservationStatus": "http://schema.org/Confirmed",
    "lodgingUnitDescription": "${offer.accommodation[0].name}",
    "underName": {
      "@type": "Person",
      "name": "${request.contact.name}"
    },
    "checkinTime": "${checkin}",
    "checkoutTime": "${checkout}"
  }
  </script>`;
  return schemaConfirmation;
};
