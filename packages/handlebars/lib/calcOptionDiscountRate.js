const accounting = require('accounting');

/**
 * Calculate the discount of all offers in an Option
 *
 * @param {QOffer[]} offers
 * @returns {float}
 */
module.exports = function calcOptionDiscountRate(offers) {
  const officialRate = offers.reduce((sum, current) => (sum += current.officialRate), 0);
  const roomRate = offers.reduce((sum, current) => (sum += current.roomRate), 0);

  let discount = (officialRate - roomRate) / (officialRate / 100);
  // reverse from positive to negative or the opposite
  discount = ~discount + 1;
  return accounting.toFixed(discount, 2);
};
