const moment = require('moment');

/**
 * @param {QOffer} offer
 * @param {string} language
 * @param {array} format
 * @returns {string}
 */
module.exports = (offer, language, format) => {
  language = language || 'en';
  format = format || 'YYYY-MM-DD';
  let checkoutDate = moment(offer.checkin)
    .locale(language)
    .add(offer.nights, 'days')
    .format(format || 'DD MMMM YYYY');
  return checkoutDate;
};
