/**
 * Calculate the total roomRate of all offers in an Option
 *
 * @param {QOffer[]} offers
 * @returns {number}
 */
module.exports = function calcOptionTotalRate(offers) {
  return offers.reduce((sum, current) => {
    const serviceTotalRate = current.serviceTotalRate || 0;
    const excludedCharges = current.excludedCharges || 0;

    sum += ((current.roomRate + excludedCharges) * current.nights) + serviceTotalRate;

    return sum;
  }, 0);
};
