'use strict';

/**
 * @param {string} text
 * @param {string} delimiter='<!--summary-->'
 * @param {number|'first'|'last'} block='first'
 * @returns {string}
 */
module.exports = function summarySplit(text, delimiter, block) {
  delimiter = delimiter || '<!--summary-->';
  block = block || 'first';

  const blocks = text.split(delimiter).map(block => block.trim());

  if (typeof block === 'number') {
    return blocks[block - 1] || null;
  } else if (block === 'first') {
    return blocks[0];
  } else if (block === 'last') {
    return blocks[blocks.length - 1];
  } else {
    return null;
  }
};
