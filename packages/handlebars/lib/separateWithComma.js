/**
 * @param {string} a1
 * @param {string} a2
 * @param {string} a3
 * @param {string} a4
 * @param {string} a5
 * @param {string} a6
 * @param {string} a7
 * @param {string} a8
 * @param {string} a9
 * @returns {string}
 */
module.exports = (a1, a2, a3, a4, a5, a6, a7, a8, a9) => {
  return [a1, a2, a3, a4, a5, a6, a7, a8, a9]
    .filter(label => {
      return typeof label == 'string' && label[0] !== '0';
    })
    .map(label => {
      return label;
    })
    .join(', ');
};
