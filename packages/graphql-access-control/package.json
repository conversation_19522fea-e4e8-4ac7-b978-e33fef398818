{"name": "@quotelier/graphql-access-control", "version": "1.0.0", "description": "graphql-access-control", "license": "MIT", "repository": "", "author": "<PERSON><PERSON><PERSON>", "keywords": [], "files": ["dest"], "main": "dest/index.js", "typings": "dest/index.d.ts", "scripts": {"build": "gulp build", "test": "gulp test", "watch": "gulp build-watch", "watch:test": "gulp test-watch"}, "peerDependencies": {"graphql": ">=0.8.0"}, "dependencies": {"joi": "^17.4.2", "jsonwebtoken": "^8.5.1", "wildcard": "^2.0.0"}, "devDependencies": {"@types/bluebird": "^3.5.2", "@types/chai": "^4.2.22", "@types/graphql": "^14.5.0", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^8.5.5", "@types/mocha": "^9.0.0", "@types/node": "^16.9.6", "chai": "^4.3.4", "graphql": "^15.6.0", "gulp": "^4", "gulp-cli": "^2.3.0", "gulp-mocha": "^8.0.0", "gulp-typescript": "^6.0.0-alpha.1", "mocha": "^9.1.1", "typescript": "^4.4.3", "yargs": "^17.2.0"}, "engines": {"node": ">=4.0.0"}, "nyc": {"include": ["src/*.ts"], "exclude": ["lib"], "extension": [".ts"], "require": ["ts-node/register"], "reporter": [], "all": true}}