'use strict';

const gulp = require('gulp');
const mocha = require('gulp-mocha');
const argv = require('yargs').argv;
const ts = require('gulp-typescript');
const tsProject = ts.createProject('tsconfig.json');

gulp.task('build', () => gulp.series(['build-ts', 'move-meta']));

gulp.task('test', async () => {
  await gulp.series(['build']);

  let filename = argv.testCase || '*';

  gulp.src(`./dest/tests/**/${filename}.test.js`).pipe(
    mocha({
      timeout: 50000,
    })
  );

});

gulp.task('test-watch', async () => {
  await gulp.series(['test']);
  gulp.watch('./src/**/*.ts', ['test']);
});

gulp.task('build-watch', async () => {
  await gulp.series(['build']);
  gulp.watch('./src/**/*.ts', ['build']);
});

gulp.task('build-ts', async () => {
  return tsProject.src().pipe(tsProject()).js.pipe(gulp.dest('./dest'));
});

gulp.task('move-meta', async () => {
  return gulp.src(['src/**/*', '!src/**/*.ts']).pipe(gulp.dest('./dest'));
});
