import {GraphQLSchema, GraphQLObjectType, GraphQLString} from 'graphql';

const Query = new GraphQLObjectType({
  name: 'RootTestQuery',
  fields: () => ({
    testProvider: {
      type: new GraphQLObjectType({
        name: 'test<PERSON>rovider',
        fields: {
          name: {
            type: GraphQLString
          },
        }
      }),
      resolve: (source, args, context, ast) => {
        return {
          name: 'Alexandros'
        };
      }
    },
    requestAccounts: {
      type: new GraphQLObjectType({
        name: 'requestAccounts',
        fields: {
          name: {
            type: GraphQLString
          },
        }
      }),
      resolve: (source, args, context, ast) => {
        return {
          name: 'Administrator'
        };
      }
    },
    requestOffers: {
      type: new GraphQLObjectType({
        name: 'requestOffers',
        fields: {
          name: {
            type: GraphQLString
          },
        }
      }),
      resolve: (source, args, context, ast) => {
        return {
          name: 'offer'
        };
      }
    }
  })
});

const Mutation = new GraphQLObjectType({
  name: 'RootTestMutation',
  fields: () => ({
    removeRequest: {
      type: new GraphQLObjectType({
        name: 'requestRequest',
        fields: {
          name: {
            type: GraphQLString
          },
        }
      }),
      resolve: (source, args, context, ast) => {
        return {
          name: 'Administrator'
        };
      }
    },
    createRequest: {
      type: new GraphQLObjectType({
        name: 'createRequest',
        fields: {
          name: {
            type: GraphQLString
          },
        }
      }),
      resolve: (source, args, context, ast) => {
        return {
          name: 'offer'
        };
      }
    }
  })
});

export const Schema = new GraphQLSchema({
  query: Query,
  mutation: Mutation
});
