import {JWTAccessControl} from '../accessControl';
import {Schema} from './fixtures/test.schema';
import {readFileSync} from 'fs';
import {join} from 'path';
import {graphql} from 'graphql';
import {sign} from 'jsonwebtoken';
import {expect} from 'chai';

describe('Access Control', () => {
    let accessControl: JWTAccessControl;
    let query: string;
    let mutation: string;
    let validContext: any;
    let invalidContext: any;
    beforeEach(() => {
        accessControl = new JWTAccessControl(Schema, 'test');
        query = readFileSync(join(__dirname, 'fixtures/test-query.graphql'), 'utf-8');
        mutation = readFileSync(join(__dirname, 'fixtures/test-mutation.graphql'), 'utf-8');
        validContext = {authorizationHeader: `Bearer ${sign({}, 'test')}`};
        invalidContext = {authorizationHeader: `Bearer ${sign({}, 'wrong-secret-key')}`};
    });

    afterEach(() => {
        accessControl.restoreSchema();
    });

    it('should deny access with invalid token', (done) => {
        accessControl.check('query.*', (jwtData, source, args, context) => true);
        graphql(accessControl.getSchema(), query, null, invalidContext, {})
            .then((res) => {
                expect(res.data.testProvider).to.be.null;
                expect(res.errors.length).to.be.greaterThan(0);
                expect(res.errors[0].message).to.contain('invalid signature');
            })
            .then(() => done())
            .catch((err) => done(err));
    });

    it('should allow access with valid token', (done) => {
        accessControl.check('query.*', (jwtData, source, args, context) => true);
        graphql(accessControl.getSchema(), query, null, validContext, {})
            .then((res) => {
                expect(res.errors).to.be.undefined;
                expect(res.data.testProvider.name).to.be.equal('Alexandros');
            })
            .then(() => done())
            .catch((err) => done(err));
    });
    describe('with custom error type', () => {
        class CustomError extends Error {
            constructor() {
                super('custom error message');
            }
        }
        beforeEach(() => {
            return accessControl.check('query.*', (jwtData, source, args, context) => {
                return new CustomError();
            })
        });

        it('should throw the custom error object', () => {
            return graphql(accessControl.getSchema(), query, null, validContext, {})
                .then((res) => {
                    expect(res.errors[0].message).to.be.equal('custom error message');
                });
        });
    });
    it('should deny access with negative access decorator', (done) => {
        accessControl.check('mutation.*', (jwtData, source, args, context) => false);
        const context = {authorizationHeader: `Bearer ${sign({}, 'test')}`};
        graphql(accessControl.getSchema(), mutation, null, context, {})
            .then((res) => {
                expect(res.data.createRequest).to.be.null;
                expect(res.errors.length).to.be.greaterThan(0);
                expect(res.errors[0].message).to.contain('Permission denied');
            })
            .then(() => done())
            .catch((err) => done(err));
    });
});