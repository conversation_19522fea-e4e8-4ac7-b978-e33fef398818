import {Decorator} from '../decorator';
import {Schema} from './fixtures/test.schema';
import {graphql} from 'graphql';
import {expect} from 'chai';
import {readFileSync} from 'fs';
import {join} from 'path';

describe('Decorator', () => {
    let decorator: Decorator;
    let query: string;
    let mutation: string;
    beforeEach(() => {
        decorator = new Decorator(Schema);
        query = readFileSync(join(__dirname, 'fixtures/test-query.graphql'), 'utf-8');
        mutation = readFileSync(join(__dirname, 'fixtures/test-mutation.graphql'), 'utf-8');
    });

    afterEach(() => {
        decorator.restoreSchema();
    });

    it('should decorate multiple resolve using wildcard', (done) => {
        decorator.decorateMultiple('query.*', () => {
            return {name: 'decorated'};
        });

        graphql(decorator.getSchema(), query, null, null, {})
            .then((res) => {
                expect(res.data.testProvider.name).to.be.equal('decorated');
            })
            .then(() => graphql(decorator.getSchema(), mutation, null, null, {}))
            .then((res) => {
                expect(res.data.createRequest.name).to.be.equal('offer');
            })
            .then(() => done())
            .catch((err) => done(err));
    });

    it('should provide unmodified results', (done) => {
        graphql(decorator.getSchema(), query, null, null, {})
            .then((res) => {
                expect(res.data.testProvider.name).to.be.equal('Alexandros');
                done();
            })
            .catch((err) => done(err));
    });

    it('should create the schema index correctly', () => {
        const paths = decorator.getIndex()
            .map((index) => index.path);
        [
            'query.testProvider',
            'query.requestAccounts',
            'query.requestOffers',
            'mutation.removeRequest',
            'mutation.createRequest'
        ].forEach((path) => {
            expect(paths).to.contain(path);
        });
    });

    describe('duplicate decoration', () => {
        beforeEach(() => {
            decorator.decorate('query.testProvider', (resolver, source, args, context) => {
                const res = <any>resolver(source, args, context);
                res.name = res.name + '-1';
                return res;
            });
            decorator.decorate('query.testProvider', (resolver, source, args, context) => {
                const res = <any>resolver(source, args, context);
                res.name = res.name + '-2';
                return res;
            });
        });
        it('should provide duplicated modification', (done) => {
            graphql(decorator.getSchema(), query, null, null, {})
                .then((res) => {
                    expect(res.data.testProvider.name).to.be.equal('Alexandros-1-2');
                    done();
                })
                .catch((err) => done(err));
        });
    });

    describe('result modification', () => {
        beforeEach(() => {
            decorator.decorate('query.testProvider', (resolver, source, args, context) => {
                let res = <any>resolver(source, args, context);
                res.name = res.name + '-decorated';
                return res;
            });
        });

        it('should provide the modification of the decorator', (done) => {
            graphql(decorator.getSchema(), query, null, null, {})
                .then((res) => {
                    expect(res.data.testProvider.name).to.be.equal('Alexandros-decorated');
                    done();
                })
                .catch((err) => done(err));
        });

        it('should provide the modification of the decorator', (done) => {
            graphql(decorator.getSchema(), query, null, null, {})
                .then((res) => {
                    expect(res.data.testProvider.name).to.be.equal('Alexandros-decorated');
                    done();
                })
                .catch((err) => done(err));
        });
    })
});
