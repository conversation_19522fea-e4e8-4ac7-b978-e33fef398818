import * as <PERSON><PERSON> from 'joi';

export const AccessControlContextSchema = Joi.object().keys({
    authorizationHeader: Joi.string()
        .when("__skip_jwt_check__", {
            is: false,
            then: Joi.required()
        })
        .when("__skip_jwt_check__", {
            is: true,
            then: Joi.optional()
        }),
    __skip_jwt_check__: Joi.boolean().default(false)
}).unknown(true);
