import {
    GraphQLSchema, GraphQLObjectType, GraphQLTypeResolver,
    GraphQLField, GraphQLFieldResolver, GraphQLAbstractType
} from 'graphql';

const wildcard = require('wildcard');

export interface ISchemaIndexItem {
    path: string;
    field: GraphQLField<any, any>;
}

export interface IDecorationHistoryEntry {
    path: string;
    originalResolver: GraphQLFieldResolver<any, any>;
    fieldTarget: GraphQLField<any, any>;
}

export interface IDecoratorFunc {
    (resolver: Function, source: any, args: any, context: any, ast: GraphQLAbstractType): any;
}

export class Decorator {

    protected decorationHistory: IDecorationHistoryEntry[] = [];

    constructor(private schema: GraphQLSchema) {
    }

    static buildSchemaPathIndex(schema: GraphQLSchema): ISchemaIndexItem[] {
        let mixedItems = <any>[
            {type: schema.getMutationType(), alias: 'mutation'},
            {type: schema.getQueryType(), alias: 'query'},
            {type: schema.getSubscriptionType(), alias: 'subscription'}
        ].filter((entry) => entry.type)
            .map((entry) => {
                const fields = entry.type.getFields();
                const fieldNames = Object.keys(fields);
                return fieldNames.map((name) => {
                    return {
                        path: `${entry.alias}.${name}`,
                        field: fields[name]
                    };
                });
            });
        return [].concat.apply([], mixedItems);
    }

    public getIndex(): ISchemaIndexItem[] {
        return Decorator.buildSchemaPathIndex(this.schema);
    }

    decorateMultiple(selector: string, func: IDecoratorFunc) {
        const index = Decorator.buildSchemaPathIndex(this.schema);
        const entriesToDecorate = index.filter((item) => {
            return wildcard(selector, item.path);
        });
        entriesToDecorate.forEach((entry) => {
            this.decorate(entry.path, func);
        })
    }

    decorate(path: string, func: IDecoratorFunc) {
        const validSelection = /^(query|mutation|subscription)\.\w+$/.exec(path);
        if (!validSelection) {
            throw new Error('Invalid selector!');
        }

        const parts = path.split('.');
        const baseTypeName = parts[0];
        const fieldType = parts[1];
        let baseType: GraphQLObjectType;

        if (baseTypeName === 'query') {
            baseType = this.schema.getQueryType();
        } else if (baseTypeName === 'mutation') {
            baseType = this.schema.getMutationType();
        }

        if (!baseType) {
            throw new Error(`Schema does not provider '${baseTypeName}' implementation.`);
        }
        const fields = baseType.getFields();
        const fieldTarget: GraphQLField<any, any> = fields[fieldType];

        if (!fieldTarget) {
            throw new Error(`'${baseTypeName}' does not provide '${fieldType}' implementation.`);
        }
        this.decorationHistory.push({
            path: path,
            originalResolver: fieldTarget.resolve,
            fieldTarget: fieldTarget
        });
        fieldTarget.resolve = func.bind(this, fieldTarget.resolve);
    }

    restoreSchema() {
        this.decorationHistory.reverse().forEach((item) => {
            item.fieldTarget.resolve = item.originalResolver;
        });
        this.decorationHistory = [];
    }

    getSchema() {
        return this.schema;
    }
}
