import {Decorator} from './decorator';
import {GraphQLSchema, GraphQLAbstractType} from 'graphql';
import {AccessControlContextSchema} from './schemas';
import {verify} from 'jsonwebtoken';

export interface ICheckerFunc {
    (jwtData: any, source: any, args: any, context: any, ast: GraphQLAbstractType): boolean | string | Error | Promise<boolean | string | Error>;
}

interface IContext {
    authorizationHeader: string
    __skip_jwt_check__: boolean
}

export class JWTAccessControl {
    protected decorator: Decorator;
    protected secretToken: string;

    constructor(schema: GraphQLSchema, secretToken: string) {
        this.secretToken = secretToken;
        this.decorator = new Decorator(schema);
    }

    protected denyHandler(message?: string) {
        if (message) {
            throw new Error(message);
        } else {
            throw new Error('Permission denied');
        }
    }

    public check(selector: string | string[], func: ICheckerFunc) {
        if (selector instanceof Array) {
            return selector.forEach((selector) => {
                this.check(selector, func);
            });
        }
        this.decorator.decorateMultiple(selector, (resolver, source, args, context: IContext, ast: GraphQLAbstractType) => {
            let isAllowed: boolean = false;
            let jwtData: any = null;
            // Validate context object schema
            const res: any = AccessControlContextSchema.validate(context);
            if (res.error) {
                this.denyHandler(res.error.message);
            } else {
                context = res.value;
            }

            // JWT Verification
            if (!context.__skip_jwt_check__) {
                const grp = /Bearer\s+([\w.-]+)/.exec(context.authorizationHeader);
                if (!grp || grp.length !== 2) {
                    isAllowed = false;
                } else {
                    try {
                        jwtData = verify(grp[1], this.secretToken);
                    } catch (err) {
                        this.denyHandler(err.message);
                    }
                    (<any>context).access = jwtData;
                    isAllowed = true;
                }
            } else {
                isAllowed = true;
            }
            if (!isAllowed) {
                this.denyHandler('JWT Authorization header is invalid');
            }
            return Promise.resolve()
                .then(() => func(jwtData, source, args, context, ast))
                .then((result) => {
                    if (result instanceof Error) {
                        throw result;
                    } else if (typeof result === 'string') {
                        this.denyHandler(result);
                    } else if (!result) {
                        this.denyHandler();
                    } else {
                        return resolver(source, args, <any>context, ast);
                    }
                });
        });
    }

    restoreSchema() {
        this.decorator.restoreSchema();
    }

    getSchema() {
        return this.decorator.getSchema();
    }
}