'use strict';
const Bluebird = require('bluebird');
const fs = require('fs');
const path = require('path');
const debug = require('debug')('quotelier:publishing:helper:dataProvider');

class GraphQLDataProvider {
  /**
   *
   * @param {Lambda} lambda
   * @param {string} functionName
   */
  constructor(lambda, functionName) {
    this.lambda = lambda;
    this.functionName = functionName;
  }

  /**
   *
   * @param {string} queryFile
   * @param {object} variables
   * @returns {Promise.<Object>}
   */
  queryFile(queryFile, variables) {
    if (queryFile[0] !== '.' && queryFile[0] !== '/') {
      queryFile = path.resolve(path.join(__dirname, '../graphql', queryFile));
    }

    return new Bluebird((resolve, reject) => {
      if (fs.existsSync(queryFile)) {
        const query = fs.readFileSync(queryFile, 'utf-8');
        resolve(query);
      } else {
        reject(new Error(`The query file '${queryFile}' does not exist.`));
      }
    }).then(query => {
      return this.query(query, variables);
    });
  }

  /**
   * @param {string} query
   * @param {object} variables
   * @returns {Promise.<object>}
   */
  query(query, variables) {
    debug('query data', { query: query, variables: variables });
    const invocationParameters = {
      FunctionName: this.functionName,
      InvocationType: 'RequestResponse',
      LogType: 'None',
      Payload: JSON.stringify({
        body: {
          query: query,
          variables: JSON.stringify(variables),
        },
      }),
    };
    return this.lambda
      .invoke(invocationParameters)
      .promise()
      .then(result => {
        /**
         * Parse results and ensure the correctness of the results.
         */
        debug('Invocation response', result);
        if (result.StatusCode !== 200) {
          throw new Error('Unexpected invocation response');
        }
        const functionResponse = JSON.parse(result.Payload);
        if (functionResponse.errors && functionResponse.errors.length) {
          throw new Error(functionResponse.errors.pop().message);
        }
        return functionResponse.data;
      })
      .catch(err => {
        debug('Invocation failed', {
          message: err.message,
        });
        throw err;
      });
  }
}

module.exports = GraphQLDataProvider;
