'use strict';
const debug = require('debug')('quotelier:publishing:helper:renderer');

class RequestBasedTemplateRenderer {
  /**
   *
   * @param {Lambda} lambda
   * @param {string} functionName
   */
  constructor(lambda, functionName) {
    this.functionName = functionName;
    this.lambda = lambda;
  }

  /**
   * @param {QRequest} request
   * @param {object} context
   * @param {string=request_template} templateId
   * @param {string=templates} templateType
   * @returns {Promise<{metadata: object, content: string}>}
   */
  render(request, context, templateId, templateType) {
    templateId = templateId === undefined ? request.template : templateId;
    templateType = templateType === undefined ? 'templates' : templateType;

    const params = {
      FunctionName: this.functionName,
      InvocationType: 'RequestResponse',
      LogType: 'None',
      Payload: JSON.stringify({
        id: templateId,
        accountName: request.request.accountName,
        propertyCode: request.request.propertyCode,
        language: request.language,
        type: templateType,
        data: context,
      }),
    };

    return this.lambda
      .invoke(params)
      .promise()
      .then(result => {
        /**
         * Parse results and ensure the correctness of the results.
         */
        debug('Invocation response', result);
        if (result.StatusCode !== 200) {
          throw new Error('Unexpected invocation response');
        }
        const functionResponse = JSON.parse(result.Payload);
        if (functionResponse.errorType && functionResponse.errorMessage) {
          throw new Error(functionResponse.errorMessage);
        }

        return functionResponse;
      })
      .catch(err => {
        debug('Invocation failed', { message: err.message });
        throw err;
      });
  }
}

module.exports = RequestBasedTemplateRenderer;
