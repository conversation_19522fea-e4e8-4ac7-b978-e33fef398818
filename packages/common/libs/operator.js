'use strict';

class OperatorStorageService {
  /**
   * 
   * @param {AWS.S3} s3 
   * @param {string} bucketName 
   */
  constructor(s3, bucketName) {
    this.s3 = s3;
    this.bucketName = bucketName;
  }

  /**
   * @param {QRequest} request
   *
   * @returns {Promise<QOperatorDetails>}
   */
  getOperatorByRequest(request) {
    const key = `${request.request.accountName}/${request.language}/${request.operatorId}`;
    return this.s3
      .getObject({
        Bucket: this.bucketName,
        Key: key,
      })
      .promise()
      .then(response => {
        let operator;
        try {
          operator = JSON.parse(response.Body.toString('utf-8'));
        } catch (e) {
          operator = null;
        }
        if (operator) {
          return operator;
        } else {
          throw new Error('Failed to parse operator file: ' + key);
        }
      })
      .catch(err => {
        if (err.code === 'NoSuchKey') {
          err.message = `Operator with id "${request.operatorId}" not found.`;
          err.type = 'NOT_FOUND';
        }
        throw err;
      });
  }
}

module.exports = {
  OperatorStorageService,
};
