'use strict';
const ddbUtilities = require('./dynamoUtilities');

class TagsStorageService {
  /**
     * @param {AWS} client
     * @param {string} tableName 
     */
  constructor(client, tableName) {
    this.client = client;
    this.tableName = tableName;
  }

  /**
     * @param {string} operatorId 
     */
  getTagsByOperatorId(operatorId) {
    return ddbUtilities
      .getItem(this.client, this.tableName, operatorId, 'operatorId')
      .then(item => {
        return item && item.tags && item.tags.length ? item.tags : [];
      });
  }

  /**
     * 
     * @param {string} operatorId 
     * @param {Function} tagUpdaterFn 
     */
  __updateTags(operatorId, tagUpdaterFn) {
    let tagsToReturn = [];
    return ddbUtilities
      .getItem(this.client, this.tableName, operatorId, 'operatorId')
      .then(
        /**
             * @param {{operatorId: string, tags: string[]}} item
             */
        item => {
          if (!item) {
            tagsToReturn = tagUpdaterFn([], 'new');

            // not needed to insert a new empty tag record
            if (!tagsToReturn.length) {
              return tagsToReturn;
            }

            return ddbUtilities.put(this.client, this.tableName, {
              operatorId: operatorId,
              tags: tagsToReturn,
            });
          } else {
            tagsToReturn = tagUpdaterFn(item.tags, 'existing');
            return ddbUtilities.update(
              this.client,
              this.tableName,
              {
                operatorId: operatorId,
              },
              {
                tags: tagsToReturn,
              }
            );
          }
        }
      )
      .then(() => tagsToReturn);
  }

  /**
     * 
     * @param {string} operatorId 
     * @param {string[]} tags 
     */
  removeTags(operatorId, tags) {
    return this.__updateTags(operatorId, (existingTags, status) => {
      if (status === 'new') {
        return [];
      } else if (status === 'existing') {
        return existingTags.filter(tag => tags.indexOf(tag) === -1);
      } else {
        throw new Error('Unhandled status:' + status);
      }
    });
  }

  /**
     * 
     * @param {string} operatorId 
     */
  removeAll(operatorId) {
    return this.__updateTags(operatorId, (existingTags, status) => []);
  }

  /**
     * 
     * @param {string} operatorId 
     * @param {string[]} tags 
     */
  addTags(operatorId, tags) {
    return this.__updateTags(operatorId, (existingTags, status) => {
      if (status === 'new') {
        return tags;
      } else if (status === 'existing') {
        tags = existingTags.concat(tags);
        return tags.filter((tag, pos) => tags.indexOf(tag) == pos);
      } else {
        throw new Error('Unhandled status:' + status);
      }
    });
  }
}

module.exports = {
  TagsStorageService,
};
