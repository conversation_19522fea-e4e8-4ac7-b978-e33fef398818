'use strict';

class AccountStorageService {
  constructor(dynamoClient, tableName) {
    this.client = dynamoClient;
    this.tableName = tableName;
  }

  /**
   * @param {string} accountName 
   * @returns {Promise<QAccount>}
   */
  getAccountByName(accountName) {
    return new Promise((resolve, reject) => {
      this.client.get(
        {
          TableName: this.tableName,
          Key: {
            accountName: accountName,
          },
        },
        (err, response) => {
          if (err) {
            reject(err);
          } else if (!response.Item) {
            reject(new Error(`Account "${accountName}" not found`));
          } else {
            resolve(response.Item);
          }
        }
      );
    });
  }
}

module.exports = {
  AccountStorageService,
};
