'use strict';

require('../types');
const dynamoUtils = require('./dynamoUtilities');
const moment = require('moment');

class RequestStorageService {
  /**
   * @param {DocumentClient} dynamoClient
   * @param {string} tableName
   */
  constructor(dynamoClient, tableName) {
    this.client = dynamoClient;
    this.tableName = tableName;
  }

  /**
   *
   * @param {string} requestId
   * @param {Object} attributesUpdate
   */
  updateRecord(requestId, attributesUpdate) {
    return dynamoUtils.update(
      this.client,
      this.tableName,
      {
        id: requestId,
      },
      attributesUpdate,
      'ALL_NEW'
    );
  }

  /**
   * Converts a datetime string with timezone specification
   * to a timestamp shifted to the current timezone.
   *
   * @param {string} datetime
   */
  static normalizeDatetimeToLocalTimestamp(datetime) {
    let convertedTimezoneMoment = moment(datetime);
    let momentDistanceMilliseconds = convertedTimezoneMoment.diff(Date.now());
    let momentDistanceSeconds = parseInt(momentDistanceMilliseconds / 1000);
    let momentDistanceMinutes = parseInt(momentDistanceSeconds / 60);
    let momentDistanceHours = parseInt(momentDistanceMinutes / 60);

    return moment(Date.now()).add(momentDistanceHours, 'h').toDate().getTime();
  }

  /**
   * Todo: store this somewhere else, its not a part of the requestStorage
   * @todo: Declare offer type
   * @param {QOffer[]} offers
   * @returns {QOffer}
   */
  static getEarliestOfferByReleaseAt(offers) {
    let earliestOffer, earliestOfferIndex, earliestTimestamp;

    offers.forEach((offer, index) => {
      if (!offer.releaseAt) {
        return;
      }

      let ts = moment(offer.releaseAt).toDate().getTime();
      if (earliestTimestamp && earliestTimestamp < ts) {
        return;
      }

      earliestOffer = offer;
      earliestOfferIndex = index;
      earliestTimestamp = ts;
    });
    if (
      earliestOffer !== undefined &&
      earliestOfferIndex !== undefined &&
      earliestTimestamp !== undefined
    ) {
      return {
        offer: earliestOffer,
        index: earliestOfferIndex,
        timestamp: earliestTimestamp,
      };
    } else {
      return null;
    }
  }

  /**
   *
   * @param {string} requestId
   * @returns {Promise<QRequest>}
   */
  getRequestById(requestId) {
    return this.client
      .get({
        TableName: this.tableName,
        Key: {
          id: requestId,
        },
      })
      .promise()
      .then(response => {
        if (!response.Item) {
          let err = new Error(`Request "${requestId}" not found.`);
          err.type = 'NOT_FOUND';
          throw err;
        } else {
          return response.Item;
        }
      });
  }
}

const REQUEST_STATE = {
  NEW: 'new',
  DRAFT: 'draft',
  PENDING: 'pending',
  SENT: 'sent',
  ACCEPTED: 'accepted',
  REJECTED: 'rejected',
  OPENED: 'opened',
  EXPIRED: 'expired',
  CONFIRMED: 'confirmed',
  ARCHIVED: 'archived',
};

const STATE_MOVES = {};

STATE_MOVES[REQUEST_STATE.NEW] = [
  REQUEST_STATE.DRAFT,
  REQUEST_STATE.EXPIRED,
  REQUEST_STATE.ARCHIVED,
];
STATE_MOVES[REQUEST_STATE.DRAFT] = [
  REQUEST_STATE.PENDING,
  REQUEST_STATE.EXPIRED,
  REQUEST_STATE.ARCHIVED,
];
STATE_MOVES[REQUEST_STATE.PENDING] = [REQUEST_STATE.SENT, REQUEST_STATE.EXPIRED];
STATE_MOVES[REQUEST_STATE.SENT] = [
  REQUEST_STATE.OPENED,
  REQUEST_STATE.EXPIRED,
  REQUEST_STATE.DRAFT,
  REQUEST_STATE.ARCHIVED,
];
STATE_MOVES[REQUEST_STATE.OPENED] = [
  REQUEST_STATE.ACCEPTED,
  REQUEST_STATE.REJECTED,
  REQUEST_STATE.EXPIRED,
  REQUEST_STATE.DRAFT,
  REQUEST_STATE.ARCHIVED,
];
STATE_MOVES[REQUEST_STATE.ACCEPTED] = [
  REQUEST_STATE.CONFIRMED,
  REQUEST_STATE.EXPIRED,
  REQUEST_STATE.DRAFT,
  REQUEST_STATE.ARCHIVED,
];
STATE_MOVES[REQUEST_STATE.CONFIRMED] = [
  REQUEST_STATE.EXPIRED,
  REQUEST_STATE.DRAFT,
  REQUEST_STATE.ARCHIVED,
];
STATE_MOVES[REQUEST_STATE.REJECTED] = [REQUEST_STATE.DRAFT, REQUEST_STATE.ARCHIVED];
STATE_MOVES[REQUEST_STATE.EXPIRED] = [REQUEST_STATE.DRAFT, REQUEST_STATE.ARCHIVED];
STATE_MOVES[REQUEST_STATE.ARCHIVED] = [];

class RequestStateService {
  /**
   * @param {QRequest} request
   */
  constructor(request) {
    this.request = request;

    /**
     * @type {string[]}
     */
    this.availableStates = [];

    Object.keys(STATE_MOVES).map(state => STATE_MOVES[state].concat([state])).forEach(states => {
      states.forEach(state => {
        if (this.availableStates.indexOf(state) === -1) {
          this.availableStates.push(state);
        }
      });
    });
  }

  /**
   * @param {string} state
   * @returns {boolean}
   */
  isStateAvailable(state) {
    return this.availableStates.indexOf(state) !== -1;
  }

  /**
   * @returns {string[]}
   */
  getNextPossibleStates() {
    if (!this.isStateAvailable(this.request.state)) {
      throw new Error(`State "${this.request.state}" is not available.`);
    }
    return STATE_MOVES[this.request.state] || [];
  }

  /**
   *
   * @param {string} state
   */
  canMoveStateTo(state) {
    return this.getNextPossibleStates().indexOf(state) !== -1;
  }
}

const REQUEST_ACTIONS = {
  OWN: 'own',
  FOLLOWUP: 'followup',
  EXPIRE: 'expire',
  EXTEND: 'extend',
  CONFIRM: 'confirm',
  REVERT: 'revert',
  CANCEL: 'cancel',
  EDIT: 'edit',
  ARCHIVE: 'archive',
};

const STATE_ACTION_FLOW = {};

STATE_ACTION_FLOW[REQUEST_STATE.NEW] = [REQUEST_ACTIONS.OWN, REQUEST_ACTIONS.ARCHIVE];
STATE_ACTION_FLOW[REQUEST_STATE.DRAFT] = [REQUEST_ACTIONS.EDIT, REQUEST_ACTIONS.ARCHIVE];
STATE_ACTION_FLOW[REQUEST_STATE.SENT] = [
  REQUEST_ACTIONS.FOLLOWUP,
  REQUEST_ACTIONS.EXPIRE,
  REQUEST_ACTIONS.REVERT,
  REQUEST_ACTIONS.ARCHIVE,
];
STATE_ACTION_FLOW[REQUEST_STATE.OPENED] = [
  REQUEST_ACTIONS.FOLLOWUP,
  REQUEST_ACTIONS.EXTEND,
  REQUEST_ACTIONS.EXPIRE,
  REQUEST_ACTIONS.REVERT,
  REQUEST_ACTIONS.ARCHIVE,
];
STATE_ACTION_FLOW[REQUEST_STATE.ACCEPTED] = [
  REQUEST_ACTIONS.CONFIRM,
  REQUEST_ACTIONS.FOLLOWUP,
  REQUEST_ACTIONS.EXTEND,
  REQUEST_ACTIONS.REVERT,
  REQUEST_ACTIONS.EXPIRE,
  REQUEST_ACTIONS.ARCHIVE,
];
STATE_ACTION_FLOW[REQUEST_STATE.CONFIRMED] = [
  REQUEST_ACTIONS.REVERT,
  REQUEST_ACTIONS.EXPIRE,
  REQUEST_ACTIONS.ARCHIVE,
];
STATE_ACTION_FLOW[REQUEST_STATE.REJECTED] = [REQUEST_ACTIONS.REVERT, REQUEST_ACTIONS.ARCHIVE];
STATE_ACTION_FLOW[REQUEST_STATE.EXPIRED] = [REQUEST_ACTIONS.REVERT, REQUEST_ACTIONS.ARCHIVE];

class RequestActionService {
  /**
   * @param {QRequest} request
   */
  constructor(request) {
    this.request = request;
    this.stateSerivce = new RequestStateService(request);
  }

  /**
   *
   */
  getNextPossibleActions() {
    const state = this.request.state;
    if (this.stateSerivce.isStateAvailable(state)) {
      return STATE_ACTION_FLOW[state] || [];
    } else {
      throw new Error(`State ${state} is not available.`);
    }
  }

  /**
   * @param {string} action
   */
  isActionAvailable(action) {
    let available = false;
    for (let actionName of Object.keys(REQUEST_ACTIONS)) {
      if (REQUEST_ACTIONS[actionName] === action) {
        available = true;
        break;
      }
    }
    return available;
  }

  /**
   *
   */
  canPerformAction(action) {
    const state = this.request.state;
    if (this.stateSerivce.isStateAvailable(state)) {
      return this.getNextPossibleActions().indexOf(action) !== -1;
    } else {
      throw new Error(`State ${state} is not available`);
    }
  }
}

module.exports = {
  RequestStorageService,
  RequestStateService,
  RequestActionService,
  REQUEST_STATE,
  REQUEST_ACTIONS,
};
