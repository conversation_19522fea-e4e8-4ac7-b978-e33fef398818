'use strict';

require('../types');

/**
 * @alias common.Invoker
 */
class Invoker {
  /**
   * @param {Lambda} lambda
   * @param {string} emailingFunctionName
   * @param {string} templatingFunctionName
   */
  constructor(lambda, emailingFunctionName, templatingFunctionName) {
    this.emailingFunctionName = emailingFunctionName;
    this.templatingFunctionName = templatingFunctionName;
    this.lambda = lambda;
  }

  /**
   *
   * @param {string} to
   * @param {string} subject
   * @param {string} content
   * @param {string} from
   * @param {string} fromName
   * @param {Object<string, string>} metadata
   * @return {Promise<any>}
   */
  send(to, subject, content, from, fromName, metadata) {
    return this.lambda
      .invoke({
        FunctionName: this.emailingFunctionName,
        InvocationType: 'Event',
        LogType: 'None',
        Payload: JSON.stringify({
          to: to,
          from: from,
          fromName: fromName,
          subject: subject,
          content: content,
          metadata: metadata || {},
        }),
      })
      .promise();
  }

  /**
   * @param {string} accountName
   * @param {string} property
   * @param {string} language
   * @param {string} template
   * @param {any} context
   * @return {Promise<{content: string, metadata: {}}>}
   */
  renderTemplate(accountName, property, language, template, context) {
    const payload = {
      id: template,
      accountName: accountName,
      propertyCode: property,
      language: language,
      type: 'emailTemplates',
      data: context,
    };
    return this.lambda
      .invoke({
        FunctionName: this.templatingFunctionName,
        InvocationType: 'RequestResponse',
        LogType: 'None',
        Payload: JSON.stringify(payload),
      })
      .promise()
      .then(response => {
        const lambdaResponse = JSON.parse(response.Payload);
        if (lambdaResponse.errorMessage && lambdaResponse.Error) {
          throw lambdaResponse.Error;
        } else {
          return lambdaResponse;
        }
      });
  }
}

module.exports = {
  Invoker,
};
