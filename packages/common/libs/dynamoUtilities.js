'use strict';

// Todo: Create class simple dynamo to provide an abstract simple interface for internal usage

/**
 * Use this function to perform simple record updates with specific key definitions.
 *
 * ABSTRACTION OF:
 *  http://docs.aws.amazon.com/AWSJavaScriptSDK/latest/AWS/DynamoDB/DocumentClient.html#update-property
 *
 * Example:
 *
 * ```
 * update(client, 'example-table', {id: 'example-record'}, {
 *  'name': 'the new name'
 * });
 *
 * update(client, 'example-table', {id: 'example-record'}, {
 *  '!details.phone': '0020331014' // update a nested object attribute
 * });
 * ```
 *
 * Use "!" as mark to define the raw key expression definition-update.
 *
 * @param {DocumentClient} client
 * @param {string} tableName
 * @param {Object} keys
 * @param {Object<string, any>} updateMap
 * @param {string} [returnValues='UPDATED_NEW']
 */
function update(client, tableName, keys, updateMap, returnValues) {
  returnValues = returnValues || 'UPDATED_NEW';
  const params = {
    TableName: tableName,
    Key: keys,
    UpdateExpression: '',
    ReturnValues: returnValues,
    ExpressionAttributeValues: {},
  };
  let rawPrefixCounter = 0;
  let updateExpressions = [];
  Object.keys(updateMap).forEach(function(key) {
    let value = updateMap[key];
    if (key[0] === '!') {
      key = key.slice(1, key.length);
      let rawKey = `rawValueHolder${rawPrefixCounter}`;
      updateExpressions.push(`${key} = :${rawKey}`);
      params.ExpressionAttributeValues[`:${rawKey}`] = value;
      rawPrefixCounter++;
    } else {
      if (params['ExpressionAttributeNames'] === undefined) {
        params['ExpressionAttributeNames'] = {};
      }
      updateExpressions.push(`#${key} = :${key}`);
      params.ExpressionAttributeNames[`#${key}`] = key;
      params.ExpressionAttributeValues[`:${key}`] = value;
    }
  });
  params.UpdateExpression = `SET ${updateExpressions.join(', ')}`;
  return client.update(params).promise();
}

function getItem(client, tableName, id, idName) {
  let key = {};
  key[idName || 'id'] = id;
  return client
    .get({
      Key: key,
      TableName: tableName,
    })
    .promise()
    .then(response => {
      return response.Item;
    });
}

/**
 *
 * @param {AWS.DynamoDB.DocumentClient} client
 * @param {string} tableName
 * @param {object} data
 */
function put(client, tableName, data) {
  return client
    .put({
      TableName: tableName,
      Item: data,
    })
    .promise();
}

module.exports = {
  update,
  getItem,
  put,
};
