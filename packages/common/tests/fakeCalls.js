'use strict';
const Bluebird = require('bluebird');
const fixtures = require('./fixtures');
const lambda = {
  templating: {
    REQUEST_RENDERING_OK: () => {
      return {
        promise: () => {
          return Bluebird.resolve({
            StatusCode: 200,
            Payload: fixtures.get('publishing/render-invocation-payload.json', false),
          });
        },
      };
    },
  },
  graphql: {
    OPERATOR_OK: () => {
      return {
        promise: () => {
          return Bluebird.resolve(fixtures.get('graphql/operator.ok.response.json'));
        },
      };
    },
    OPERATOR_ERROR_NOT_PROVIDED: () => {
      return {
        promise: () => {
          return Bluebird.resolve(
            fixtures.get('graphql/operator.error.not-provided.response.json')
          );
        },
      };
    },
  },
};

const dataProvider = {
  OK: () => {
    return Bluebird.resolve(
      fixtures.get('publishing/mock-context-invocation-response-payload.json').data
    );
  },
  ERROR_INVALID_ID: () => {
    return Bluebird.reject({
      data: { request: null },
      errors: [
        {
          message: 'Invalid request id',
          locations: [{ line: 2, column: 5 }],
          path: ['request'],
        },
      ],
    });
  },
};

const renderer = {
  OK: () => {
    return Bluebird.resolve(fixtures.get('publishing/render-invocation-payload.json'));
  },
};

const fetchProposalContext = {
  OK: () => {
    return {
      promise: () => {
        return Bluebird.resolve({
          StatusCode: 200,
          Payload: fixtures.get('publishing/mock-context-invocation-response-payload.json', false),
        });
      },
    };
  },
  ERROR_INVALID_ID: () => {
    return {
      promise: () => {
        return Bluebird.resolve({
          StatusCode: 200,
          Payload: JSON.stringify({
            data: { request: null },
            errors: [
              {
                message: 'Invalid request id',
                locations: [{ line: 2, column: 5 }],
                path: ['request'],
              },
            ],
          }),
        });
      },
    };
  },
};

module.exports = {
  dataProvider,
  renderer,
  lambda,
  fetchProposalContext,
};
