'use strict';

const services = require('../../libs/request');
const expect = require('chai').expect;
const moment = require('moment');
const fixtures = require('../fixtures');

process.env.TZ = 'Europe/Athens';

const getOtherStates = allowedStates => {
  return Object.keys(services.REQUEST_STATE)
    .map(stateName => services.REQUEST_STATE[stateName])
    .filter(state => allowedStates.indexOf(state) === -1);
};

describe('Request Storage Service', () => {
  let service;
  beforeEach(() => {
    service = new services.RequestStorageService();
  });

  /**
   * We expect timezone normalization when the datetime has a timezone specification.
   */
  it('should convert datetime with timezone to timestamp and shift the time to current timezone', () => {
    const conv = services.RequestStorageService.normalizeDatetimeToLocalTimestamp;
    let testDatetime = `${moment().add(1, 'day').format('YYYY-MM-DD')}T19:00:00+0100`;
    let testDateimeWithoutTZ = `${moment().add(1, 'day').format('YYYY-MM-DD')}T19:00:00`;
    let expirationMoment = moment(conv(testDatetime));
    let expirationMomentWithoutTZ = moment(conv(testDateimeWithoutTZ));
    let millisecondsDifference = expirationMoment.diff(expirationMomentWithoutTZ);
    let minutesDifference = parseInt(millisecondsDifference / 60 / 1000);
    expect(minutesDifference).to.be.greaterThan(58);
  });
});

describe('Request State Service', () => {
  let service, request;

  beforeEach(() => {
    request = fixtures.get('request/01.json');
    service = new services.RequestStateService(request);
  });

  describe('with request state "new"', () => {
    const ALLOWED_STATES = [
      services.REQUEST_STATE.DRAFT,
      services.REQUEST_STATE.EXPIRED,
      services.REQUEST_STATE.ARCHIVED,
    ];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.NEW;
    });

    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "draft"', () => {
    const ALLOWED_STATES = [
      services.REQUEST_STATE.EXPIRED,
      services.REQUEST_STATE.PENDING,
      services.REQUEST_STATE.ARCHIVED,
    ];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.DRAFT;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "pending"', () => {
    const ALLOWED_STATES = [services.REQUEST_STATE.SENT, services.REQUEST_STATE.EXPIRED];
    beforeEach(() => {
      request.state = services.REQUEST_STATE.PENDING;
    });

    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "sent"', () => {
    const ALLOWED_STATES = [
      services.REQUEST_STATE.OPENED,
      services.REQUEST_STATE.EXPIRED,
      services.REQUEST_STATE.DRAFT,
      services.REQUEST_STATE.ARCHIVED,
    ];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.SENT;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "open"', () => {
    const ALLOWED_STATES = [
      services.REQUEST_STATE.ACCEPTED,
      services.REQUEST_STATE.EXPIRED,
      services.REQUEST_STATE.REJECTED,
      services.REQUEST_STATE.DRAFT,
      services.REQUEST_STATE.ARCHIVED,
    ];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.OPENED;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "accepted"', () => {
    const ALLOWED_STATES = [
      services.REQUEST_STATE.CONFIRMED,
      services.REQUEST_STATE.EXPIRED,
      services.REQUEST_STATE.DRAFT,
      services.REQUEST_STATE.ARCHIVED,
    ];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.ACCEPTED;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "confirmed"', () => {
    const ALLOWED_STATES = [
      services.REQUEST_STATE.EXPIRED,
      services.REQUEST_STATE.DRAFT,
      services.REQUEST_STATE.ARCHIVED,
    ];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.CONFIRMED;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "rejected"', () => {
    const ALLOWED_STATES = [services.REQUEST_STATE.DRAFT, services.REQUEST_STATE.ARCHIVED];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.REJECTED;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "expired"', () => {
    const ALLOWED_STATES = [services.REQUEST_STATE.DRAFT, services.REQUEST_STATE.ARCHIVED];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.EXPIRED;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
  describe('with request state "archived"', () => {
    const ALLOWED_STATES = [];

    beforeEach(() => {
      request.state = services.REQUEST_STATE.ARCHIVED;
    });
    ALLOWED_STATES.forEach(state => {
      it(`should be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.true;
      });
    });

    getOtherStates(ALLOWED_STATES).forEach(state => {
      it(`should not be able to move to state "${state}"`, () => {
        expect(service.canMoveStateTo(state)).to.be.false;
      });
    });
  });
});

describe('Request Action Service', () => {
  /** @type {RequestActionService} */
  let service;

  /** @type {QRequest} */
  let request;

  beforeEach(() => {
    request = fixtures.get('request/01.json');
    service = new services.RequestActionService(request);
  });

  it('should return [followup and expire] as next possible actions', () => {
    request.state = services.REQUEST_STATE.SENT;
    const nextPossible = service.getNextPossibleActions();
    expect(nextPossible).to.contain(services.REQUEST_ACTIONS.FOLLOWUP);
    expect(nextPossible).to.contain(services.REQUEST_ACTIONS.EXPIRE);
  });

  it('should return [followup, expire and extend] as next possible actions', () => {
    request.state = services.REQUEST_STATE.OPENED;
    const nextPossible = service.getNextPossibleActions();
    expect(nextPossible).to.contain(services.REQUEST_ACTIONS.FOLLOWUP);
    expect(nextPossible).to.contain(services.REQUEST_ACTIONS.EXPIRE);
    expect(nextPossible).to.contain(services.REQUEST_ACTIONS.EXTEND);
  });

  it('should return availability', () => {
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.CANCEL)).to.be.true;
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.FOLLOWUP)).to.be.true;
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.EXPIRE)).to.be.true;
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.EXTEND)).to.be.true;
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.CONFIRM)).to.be.true;
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.OWN)).to.be.true;
    expect(service.isActionAvailable(services.REQUEST_ACTIONS.REVERT)).to.be.true;
    expect(service.isActionAvailable('whatever')).to.be.false;
  });

  it('should be able to perform action "followup"', () => {
    request.state = services.REQUEST_STATE.OPENED;
    expect(service.canPerformAction(services.REQUEST_ACTIONS.FOLLOWUP)).to.be.true;
  });

  it('should be able to perform action "revert"', () => {
    request.state = services.REQUEST_STATE.OPENED;
    expect(service.canPerformAction(services.REQUEST_ACTIONS.REVERT)).to.be.true;
  });
});
