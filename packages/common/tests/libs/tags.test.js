'use strict';

const tagServices = require('../../index').tags;
const AWS = require('aws-sdk');
const expect = require('chai').expect;

describe.skip('Tags', () => {
  /**
   * @var {TagsStorageService}
   */
  let service;
  const operatorId = 'TEST_USER';

  beforeEach(done => {
    const client = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    service = new tagServices.TagsStorageService(client, 'dev2-operator-tags');

    service.removeAll(operatorId).then(() => done()).catch(err => done(err));
  });

  afterEach(done => {
    service.removeAll(operatorId).then(() => done()).catch(err => done(err));
  });

  describe('existing', () => {
    beforeEach(done => {
      service.addTags(operatorId, ['XY', 'XYZ']).then(() => done()).catch(err => done(err));
    });
    it('should fetch all inserted tag', done => {
      service
        .getTagsByOperatorId(operatorId)
        .then(() => service.getTagsByOperatorId(operatorId))
        .then(tags => {
          expect(tags).to.contain('XY');
          expect(tags).to.contain('XYZ');
          done();
        })
        .catch(err => done(err));
    });
    it('should remove single inserted tags', done => {
      service
        .removeTags(operatorId, ['XYZ'])
        .then(() => service.getTagsByOperatorId(operatorId))
        .then(tags => {
          expect(tags).to.contain('XY');
          expect(tags).not.to.contain('XYZ');
          done();
        })
        .catch(err => done(err));
    });

    it('should remove all inserted tags', done => {
      service
        .removeAll(operatorId)
        .then(tags => {
          expect(tags.length).to.be.equal(0);
          done();
        })
        .catch(err => done(err));
    });
  });

  describe('empty', () => {
    it('should return empty tags', done => {
      service
        .getTagsByOperatorId(operatorId + '-SECOND')
        .then(tags => {
          expect(tags.length).to.be.equal(0);
          done();
        })
        .catch(err => done(err));
    });
    it('should insert tags', done => {
      service
        .addTags(operatorId, ['first-tag'])
        .then(() => service.getTagsByOperatorId(operatorId))
        .then(tags => {
          expect(tags.length).to.be.equal(1);
          expect(tags).to.be.contain('first-tag');
          done();
        })
        .catch(err => done(err));
    });
  });
});
