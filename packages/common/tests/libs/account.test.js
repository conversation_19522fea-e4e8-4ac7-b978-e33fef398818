'use strict';

const AWS = require('aws-sdk');
const services = require('../../libs/account');
const expect = require('chai').expect;

const REGION = 'us-east-1';
const TABLE_NAME = 'dev2-accounts';

describe.skip('Account', () => {
  let service;
  beforeEach(() => {
    const ddb = new AWS.DynamoDB.DocumentClient({
      region: REGION,
    });
    service = new services.AccountStorageService(ddb, TABLE_NAME);
  });

  it('should get account by name', done => {
    service
      .getAccountByName('DEMO')
      .then(account => {
        expect(account.accountName).to.be.equal('DEMO');
        expect(account.accountPassword.length).to.be.above(10);
      })
      .then(() => done())
      .catch(err => done(err));
  });
});
