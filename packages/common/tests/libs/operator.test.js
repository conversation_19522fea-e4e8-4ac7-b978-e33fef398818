'use strict';

const AWS = require('aws-sdk');
const operatorServices = require('../../libs/operator');
const expect = require('chai').expect;

describe.skip('Operator storage service', () => {
  let service;

  beforeEach(() => {
    const s3 = new AWS.S3();
    service = new operatorServices.OperatorStorageService(s3, 'dev2-operators');
  });

  it('should get operator marina', cb => {
    service
      .getOperatorByRequest({
        request: {
          accountName: 'DEMO',
        },
        language: 'en',
        operatorId: 'MARINA',
      })
      .then(operator => {
        expect(operator.id).to.be.equal('MARINA');
        expect(operator.email).to.be.equal('<EMAIL>');
        return cb();
      })
      .catch(err => cb(err));
  });
});
