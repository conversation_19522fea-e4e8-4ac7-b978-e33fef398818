'use strict';
const fs = require('fs');
const path = require('path');

const normPath = filePath => {
  if (filePath[0] == '/') {
    return filePath;
  } else {
    return path.join(__dirname, filePath);
  }
};

const get = (filePath, parse) => {
  filePath = normPath(filePath);
  parse = parse === undefined ? true : parse;
  const content = fs.readFileSync(filePath, 'utf-8');
  if (parse) {
    return JSON.parse(content);
  } else {
    return content;
  }
};

const save = (filePath, content) => {
  return fs.writeFileSync(filePath, content);
};

module.exports = {
  get,
  save,
};
