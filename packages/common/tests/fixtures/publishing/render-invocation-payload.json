{"metadata": {"formats": {"date": "DD-MM-YYYY", "humanDate": "MMMM D YYYY", "period": ["DD MMMM", "DD MMMM YYYY"]}, "i18n": {"opened": {"text": "ACCEPT THIS OFFER", "classes": ["positive"]}, "loading": {"text": "LOADING", "classes": ["disabled"]}, "accepted": {"text": "ACCEPTED", "classes": ["disabled", "yellow"]}, "rejected": {"text": "REJECTED", "classes": ["disabled", "negative"]}, "expired": {"text": "EXPIRED", "classes": ["disabled", "black"]}}}, "content": "\n<!DOCTYPE html>\n<html>\n\n<head>\n    <meta charset='utf-8'>\n    <meta contant='IE=edge,chrome=1' http_equiv='X-UA-Compatible'>\n    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0' name='viewport'>\n    <link href='https://quotelier.s3.amazonaws.com/assets/css/proposal.css' rel='stylesheet' type='text/css'>\n    <link href='https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.1.8/semantic.min.css' rel='stylesheet' type='text/css'>\n    <script>\n        window.__quotelier__ = JSON.parse('');\n    </script>\n</head>\n\n<body>\n    <div class='ui flatten segments'>\n        <div class=\"ui inverted page dimmer\">\n            <div class=\"content\">\n                  <div class=\"center\">\n                      <i class=\"massive icons\"><i class=\"red history icon\"></i><i class=\"corner black lock icon\"></i></i>\n                  </div>\n            </div>\n        </div>\n        <div class='ui basic flatten segment' id='header'>\n            <div class='ui container four column stackable grid'>\n                <div class='row mobile tablet only'>\n                    <div class='center aligned column'>\n                        <img src='https://dloycpjzg76ow.cloudfront.net/s/h&#x3D;180:v26/templates/2/logo.png'>\n                    </div>\n                </div>\n                <div class='row tablet only'>\n                    <div class='sixteen wide column center aligned'>\n                        <div class='ui message'>\n                            <i class='tablet big icon'></i>\n                            <i class='refresh loading icon'></i>\n                            <i class='tablet big rotated icon'></i>\n                        </div>\n                        <div class='p'>Rotate your tablet to see more details about our offer\n                        </div>\n                    </div>\n                </div>\n            </div>\n            <div class='ui container four column stackable grid'>\n                <div class='row computer only'>\n                    <div class='left floated column'>\n                        <img src='https://dloycpjzg76ow.cloudfront.net/s/h&#x3D;180:v26/templates/2/logo.png'>\n                    </div>\n                    <div class='right floated column computer only'>\n                        <div class='ui horizontal list'>\n                            <div class='item'>\n                                <a href=\"http://www.webhotelier.net/\">\n                                    <h4 class='ui icon header'>\n                                        <i class='big globe icon'></i>\n                                        <div class='content'>WEBSITE</div>\n                                    </h4>\n                                </a>\n                            </div>\n                            <div class='item'>\n                                <a href=\"mailto:<EMAIL>\">\n                                    <h4 class='ui icon header'>\n                                        <i class='big envelope icon'></i>\n                                        <div class='content'>CONTACT US</div>\n                                    </h4>\n                                </a>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class='ui secondary basic segment'>\n            <div class='ui container stackable divided grid'>\n                <div class='row'>\n                    <div class='ten wide column'>\n                        <div class='ui tiny emphasize header'>May 5 2017</div>\n                          Dear Mrs dsadsads,</br>  Thank you for your interest in Demo Hotel for your guaranteed relaxation.</br>  Below you will find our proposal and useful information about your stay.</br>  In case that you have queries, feel free to contact your holiday consultant directly.</br>  Kind regards,</br>  George</br>\n                    </div>\n                    <div class='six wide expanded column computer only'>\n                        <div class='ui basic segment contact'>\n                            <div class='ui grid'>\n                                <div class='packed row'>\n                                    <div class='expanded right aligned column'>\n                                        Reference No: 6fad14a03178\n                                    </div>\n                                </div>\n                                <div class='packed row'>\n                                    <div class='expanded column'>\n                                        <h4 class='ui image header'>\n                                            <img class='ui small rounded image' src='https://s3-eu-west-1.amazonaws.com/quotelier/securequote/avatars/male.jpg'>\n                                            <div class='content text-black'>\n                                                George Fassas\n                                                <div class='sub header'>\n                                                    Holiday Consultant\n                                                </div>\n                                            </div>\n                                        </h4>\n                                    </div>\n                                </div>\n                                <div class='packed row'>\n                                    George is your holiday consultant\n                                </div>\n                                <div class='two column packed row text-black'>\n                                    <div class='expanded column'>\n                                        <i class='ui big phone icon left floated'></i>\n                                        <div class='header'>Phone Number:</div>\n                                        <div class='ui'>+30 2310 543 345</div>\n                                    </div>\n                                    <div class='expanded column'>\n                                        <i class='ui big envelope icon left floated'></i>\n                                        <div class='header'>Email:</div>\n                                        <div class='ui'>\n                                            <a href='mailto:<EMAIL>'>\n                                            Click to\n                                            email \n                                        </a>\n                                        </div>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class='ui basic segment branding'>\n            <div class='ui container stackable grid computer only'>\n                <div class='row'>\n                    <div class='ten wide column'>\n                        <h2 class='ui header'>\n                            Demo Hotel\n                        </h2>\n                        <h4>\n                            Athens\n                        </h4>\n                        <p>The next-gen internet booking engine for smart hoteliers</p>\n<p>Are you currently wasting time and money on a poorly designed, hard-to-manage hotel booking engine? Is your current booking engine posing a limit to your demanding online sales strategy?</p>\n<p>Do you feel that you're not squeezing the best out of the Internet?</p>\n<p>WebHotelier is here with the most powerful tools ever built, letting you take total control of the Internet possibilities and boost your online reservations to the absolute maximum.</p>\n<p>Over 3000 WebHotelier clients around the world already know that when all the other systems promise, WebHotelier simply delivers.</p>\n<p>-----</p>\n<p>This is a Demo Hotel Basic Presentation</p>\n                    </div>\n                    <div class='six wide column'>\n                        <img class='ui large rounded image' src='https://dloycpjzg76ow.cloudfront.net/s/w&#x3D;960/demo/L88411.jpg'>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class='ui secondary basic segment'>\n            <div class='ui very relaxed container'>\n                <div class='row'>\n                    <div class='column m-t-lg'>\n                        <h4 class='ui center aligned header text-uc'>\n                            IT IS OUR PLEASURE\n                            <div class='emphasize'>\n                                TO CONFIRM AVAILABILITY FOR:\n                            </div>\n                        </h4>\n                    </div>\n                </div>\n                <div class='row'>\n                    <div class='ui compact flatten segment offer-block' data-offer-id='0' id='offer-0-block'>\n                        <div class='ui stackable divided grid'>\n                            <div class='row'>\n                                <div class='ten wide tablet twelve wide column computer only'>\n                                    <h2 class='ui header text-uc'>\n                                        Deluxe Suite\n                                    </h2>\n                                    <h3 class='ui header emphasize text-uc'>\n                                        Fully Flexible Rate\n                                    </h3>\n                                    <div class='ui grid'>\n                                        <div class='nine wide column computer only'>\n                                            <a class='ui large image' href='#'>\n                                                <img class='ui visible image' src='https://dloycpjzg76ow.cloudfront.net/s/w&#x3D;390/demo-del/L192633.jpg'>\n                                            </a>\n                                        </div>\n                                        <div class='sixteen wide mobile seven wide computer column'>\n                                            <div class='ui header h4'>\n                                                <a class='text-black' href='#'>\n                                                    Deluxe Suite\n                                                </a>\n                                            </div>\n                                            <div class='ui'>\n                                                \n                                            </div>\n                                        </div>\n                                    </div>\n                                </div>\n                                <div class='sixteen wide tablet mobile four wide computer column'>\n                                    <div class='ui vertical segment center aligned'>\n                                        22 June - 29 June 2017\n                                    </div>\n                                    <div class='ui vertical segment center aligned'>\n                                        1 Room, 2 Adults, 7 Nights\n                                    </div>\n                                        <div class='ui vertical segment center aligned'>\n                                            AVERAGE RATE\n                                            <div>\n                                                <strong class='ui header huge'>250.00 EUR</strong>\n                                                <span>/ night</span>\n                                            </div>\n                                        </div>\n                                    <div class='ui vertical segment center aligned'>\n                                        <div class='ui strong'>\n                                            TOTAL: 1,750.00 EUR</div>\n                                        <small>All Taxes Included</small>\n                                    </div>\n                                    <div class='ui vertical segment center aligned'>\n                                        <button class='ui fluid button text-uc offer-action-btn'\n                                            data-action-prop-loading='{\"text\":\"LOADING\",\"classes\":[\"disabled\"]}'\n                                            data-action-prop-opened='{\"text\":\"ACCEPT THIS OFFER\",\"classes\":[\"positive\"]}'\n                                            data-action-prop-accepted='{\"text\":\"ACCEPTED\",\"classes\":[\"disabled\",\"yellow\"]}'\n                                            data-action-prop-rejected='{\"text\":\"REJECTED\",\"classes\":[\"disabled\",\"negative\"]}'\n                                            data-action-prop-expired='{\"text\":\"EXPIRED\",\"classes\":[\"disabled\",\"black\"]}'\n                                            data-offer-id='0'\n                                            id='offer-0-action-btn'>\n                                        </button>\n                                        <small>\n                                            <a class='policy link' href='javascript:void(0);'>\n                                                Payment Cancellation Policy\n                                            </a>\n                                        </small>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    <div class='ui hidden divider test'></div>\n                    <div class='ui policy small modal'>\n                        <i class='close icon'></i>\n                        <div class='header'>Payment Cancellation Policy</div>\n                        <div class='content'>\n                            <div class='description'>\n                                <p>If cancelled up to 1 day before date of  arrival,no fee will be charged.If  cancelled or modified later or in case of no show, the first night will be charged.</p>\n                            </div>\n                        </div>\n                    </div>\n                    <div class='ui accept positive small modal' data-id='0'>\n                        <i class='close icon'></i>\n                        <div class='header'>\n                            Do you accept this offer?\n                        </div>\n                        <div class='content'>\n                            <div class='description'>\n                                We will reserve the room for you and contact you regarding all the different payment options that we offer.\n                            </div>\n                            <div class='ui hidden divider'></div>\n                            <form onsubmit=\"quotelier.acceptOffer(0); return false;\" class='ui form accept-offer clearfix'>\n                                <div class='ui fluid huge input'>\n                                    <input name='fullname' placeholder='Your Full Name' required min=\"18\" max=\"99\">\n                                </div>\n                                <div class='ui hidden divider'>\n                                    <div class='ui checkbox'>\n                                        <input name='policy' type='checkbox' required>\n                                        <label>\n                                            I accept the payment and cancelation policy.\n                                        </label>\n                                    </div>\n                                    <button class='ui submit positive right floated button' type=\"submit\">\n                                        Confirm\n                                    </button>\n                                </div>\n                                <div class='ui hidden divider'></div>\n                                <div class='ui error message'>\n                                    ERROR MESSAGE\n                                </div>\n                            </form>\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n        <div class='ui basic segment'>\n            <div class='ui container stackable grid'>\n                <div class='row'>\n                    <div class='twelve wide column disabled'>\n                        <div class='ui items'>\n                            <div class='item'>\n                                <div class='content'>\n                                        <div class='header'>\n                                            This offer is based upon availability and may not be applicable at a later date.\n                                        </div>\n                                    <div class='meta'>\n                                        If this proposal doesn’t leave you satisfied, please let George Fassas know.\n                                    </div>\n                                    <div class='extra'>\n                                        <button class='ui negative reject disabled button' id='reject-main-button'>\n                                        REJECT THIS OFFER\n                                    </button>\n                                    </div>\n                                </div>\n                            </div>\n                        </div>\n                    </div>\n                    <div class='four wide column computer only right aligned'>\n                        <img src='https://dloycpjzg76ow.cloudfront.net/s/h&#x3D;180:v26/templates/2/logo.png'>\n                    </div>\n                </div>\n                <div class='expanded row'>\n                    <div class='sixteen wide column mobile only center aligned'>\n                        <div class='ui message'>\n                            Visit this page on your desktop computer or tablet to see more details about our offer\n                        </div>\n                    </div>\n                </div>\n            </div>\n        </div>\n    </div>\n    <div class='ui negative reject small modal'>\n        <i class='close icon'></i>\n        <div class='header'>Thank you for letting us know!</div>\n        <div class='content'>\n            <div class='description'>\n                We’re sorry that you do not wish to proceed with our proposal. If you could offer us some feedback, it would mean a lot!\n            </div>\n            <div class='ui divider'></div>\n            <div class='form'>\n                <form id='rejection-form'>\n                    <div class='list'>\n                        <div class='item'>\n                            <div class='ui checkbox'>\n                                <input type='checkbox' value='trip_cancelled'>\n                                <label>Your offer was fine. My trip got\n                                cancelled</label>\n                            </div>\n                        </div>\n                        <div class='item'>\n                            <div class='ui checkbox'>\n                                <input type='checkbox' value='expensive'>\n                                <label>The rate is not within the price range that\n                                I had in mind</label>\n                            </div>\n                        </div>\n                        <div class='item'>\n                            <div class='ui checkbox'>\n                                <input type='checkbox' value='travel_agent'>\n                                <label>I’m going to book through a Travel\n                                Agent</label>\n                            </div>\n                        </div>\n                        <div class='item'>\n                            <div class='ui checkbox'>\n                                <input type='checkbox' value='other'>\n                                <label>Other</label>\n                            </div>\n                        </div>\n                    </div>\n                </form>\n            </div>\n        </div>\n        <div class='actions'>\n            <div onclick='quotelier.rejectRequest()' class='ui negative rejection button'>Reject</div>\n        </div>\n    </div>\n    <div class='ui accepted small modal'>\n        <i class='close icon'></i>\n        <div class='header'>Thank you!</div>\n        <div class='content'>\n            <div class='description'>\n                Your booking has been registered. Please check your inbox, the payment details have been sent to you by email.\n            </div>\n        </div>\n    </div>\n    <div class='ui rejected small modal'>\n        <i class='close icon'></i>\n        <div class='header'>Thank you for your feedback!</div>\n        <div class='content'>\n            <div class='description'>\n                We are sorry to know that you did not accept this offer. We surely hope that we will welcome you as our guests in the near future either way you book.\n            </div>\n        </div>\n    </div>\n\n    <script src='https://cdnjs.cloudflare.com/ajax/libs/jquery/2.1.4/jquery.min.js' type='text/javascript'></script>\n    <script src='https://cdnjs.cloudflare.com/ajax/libs/semantic-ui/2.1.8/semantic.min.js' type='text/javascript'></script>\n    <script>\n        $(document).ready(function () {\n            var helper = {\n                getOfferElementblocks: function () {\n                    var elements = [];\n                    $('.offer-block').each(function (index, ele) {\n                        elements.push(ele);\n                    });\n                    return elements;\n                },\n                getOfferActionBtn: function (id) {\n                    return $('#offer-' + id + '-action-btn');\n                },\n                getOffers: function () {\n                    return helper.getOfferElementblocks()\n                        .map(function (ele) {\n                            return {\n                                index: $(ele).attr('data-offer-id'),\n                                element: ele\n                            }\n                        });\n                },\n                changeState: function (id, state) {\n                    if (window.console) console.log(id, state);\n                    var btn = this.getOfferActionBtn(id);\n                    var propJson = $(btn).attr('data-action-prop-' + state);\n                    var currentPropJson = $(btn).attr('data-current-action-prop');\n                    if (!propJson)\n                        throw new Error('State \"' + state + '\" on \"' + id + '\" is not defined!');\n                    if (currentPropJson)\n                        this.removeProperties(btn, JSON.parse(currentPropJson));\n                    this.applyProperties(btn, JSON.parse(propJson));\n                    btn.attr('data-current-state', state);\n                },\n                applyProperties: function (btn, prop) {\n                    btn = $(btn);\n                    prop.classes.forEach(btn.addClass.bind(btn));\n                    btn.text(prop.text);\n                    btn.attr('data-current-action-prop', JSON.stringify(prop));\n                },\n                removeProperties: function (btn, prop) {\n                    btn = $(btn);\n                    prop.classes.forEach(btn.removeClass.bind(btn));\n                    btn.text('');\n                },\n                performAction: function (suffix, state, cb, data) {\n                    cb = cb || function(){};\n                    if (!__quotelier__[state]) {\n                        throw new Error('No action endpoint for:' + state);\n                    }\n                    $.ajax({\n                        url: __quotelier__[state] + (suffix === null ? '' : suffix),\n                        crossDomain: true,\n                        data: data || {}\n                    }).done(function (response) {\n                        cb(response);\n                    }).error(function (request, status, err) {\n                        throw err;\n                    });\n                },\n                applyRemoteStatus: function (remoteStatus) {\n                    if (['expired', 'rejected'].indexOf(remoteStatus.state) !== -1) {\n                        helper.getOffers().forEach((offer) => {\n                            helper.changeState(offer.index, remoteStatus.state);\n                        });\n                    } else if (['draft', 'new', 'revert'].indexOf(remoteStatus.state) !== -1){\n                      $('.page.dimmer').dimmer({closable: false}).dimmer('show');\n                    } else {\n                        remoteStatus.offers.forEach((offer) => {\n                            helper.changeState(offer.index, offer.accepted ? 'accepted' : 'opened')\n                        });\n                    }\n\n                    if(remoteStatus.state === 'opened') {\n                        $('#reject-main-button').removeClass('disabled');\n                    } else {\n                        $('#reject-main-button').addClass('disabled');\n                    }\n                }\n            };\n            helper.getOffers().forEach(function (offer) {\n                helper.changeState(offer.index, 'loading');\n            });\n\n            helper.performAction(null, 'opened', function(response) {\n                helper.applyRemoteStatus(response);\n            });\n            $('.offer-action-btn').click( function(e) {\n                var offerId = $(e.target).attr('data-offer-id');\n                $(\".ui.accept.positive.modal[data-id=\"+offerId+\"]\").modal('show');\n            } );\n            $('.ui.policy.modal').modal(\"attach events\",\"a.policy.link\",\"show\");\n            $('.ui.negative.reject.modal').modal(\"attach events\",\".ui.negative.reject.button\",\"show\");\n\n            window.quotelier = {\n                acceptOffer: function(offerId) {\n                    helper.performAction(offerId, 'accepted');\n                    helper.changeState(offerId, 'accepted');\n                    $('.ui.accepted.modal').modal('show');\n                    $(\".ui.accept.positive.modal[data-id=\"+offerId+\"]\").modal('hide');\n                    return false;\n                },\n                rejectRequest: function() {\n                    var selectedReasons = [];\n                    $('#rejection-form')\n                        .find('input[type=checkbox]')\n                        .each(function(index, ele) {\n                            if($(ele).is(':checked'))\n                                selectedReasons.push($(ele).val());\n                        });\n                    helper.applyRemoteStatus({\n                        state: 'rejected'\n                    });\n                    helper.performAction(null, 'rejected', null, {\n                        reasons: selectedReasons.join('.')\n                    });\n                    return false;\n                }\n            }\n        });\n    </script>\n</body>\n\n</html>\n"}