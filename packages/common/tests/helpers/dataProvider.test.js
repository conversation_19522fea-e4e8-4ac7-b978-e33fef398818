'use strict';
const AWS = require('aws-sdk');
const GraphQLDataProvider = require('../../helpers/dataProvider');
const expect = require('chai').expect;
const path = require('path');
const fakeCalls = require('../fakeCalls');
const sinon = require('sinon');

const ONLINE_TEST = process.env.ONLINE_TEST === '1' || false;
const STAGE = process.env.STAGE || 'devel';
const REGION = process.env.REGION || 'eu-central-1';

describe('Data Provider', () => {
  /**
   * @type {Lambda}
   */
  let lambda;

  /**
   * @type {GraphQLDataProvider}
   */
  let dataProvider;

  /**
   * @type {QRequest}
   */
  let mockRequest;

  beforeEach(() => {
    lambda = new AWS.Lambda({ region: REGION });
    mockRequest = Object.assign({}, require('../fixtures/request/02.json'));
    dataProvider = new GraphQLDataProvider(lambda, `graphql-service-${STAGE}-graphql`);
  });

  it('should get operator name by request-id', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.graphql.OPERATOR_OK);
    }

    dataProvider
      .query(`query getRequest($reqId: String!) {request(id: $reqId) {operator {fullName}}}`, {
        reqId: mockRequest.id,
      })
      .then(data => {
        expect(data.request).not.undefined;
        expect(data.request.operator).not.undefined;
        done();
      })
      .catch(done);
  });

  it('should invoke with defined variables and query', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.graphql.OPERATOR_OK);
    } else {
      sinon.spy(lambda, 'invoke');
    }

    dataProvider
      .query(`query getRequest($reqId: String!) {request(id: $reqId) {operator {fullName}}}`, {
        reqId: mockRequest.id,
      })
      .then(() => {
        const invocationParams = lambda.invoke.getCalls()[0].args[0];
        const payload = JSON.parse(invocationParams.Payload);

        expect(payload.body.query).to.contain('query getRequest');
        expect(payload.body.variables).not.undefined;

        const vars = JSON.parse(payload.body.variables);
        expect(vars.reqId).to.equal(mockRequest.id);
        done();
      })
      .catch(done);
  });

  it('should raise error when graphql error-message is defined', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.graphql.OPERATOR_ERROR_NOT_PROVIDED);
    }
    dataProvider
      .query(`query getRequest($reqId: String!) {request(id: $reqId) {operator {fullName}}}`, {
        notdefined: mockRequest.id,
      })
      .then(() => {
        done(new Error('Error is expected'));
      })
      .catch(err => {
        expect(err.message).to.contain('not provided');
        done();
      });
  });

  it('should open query file and execute query', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.graphql.OPERATOR_OK);
    }
    dataProvider
      .queryFile(path.join(__dirname, '../fixtures/graphql/query-request.graphql'), {
        requestId: mockRequest.id,
      })
      .then(data => {
        expect(data.request).not.undefined;
        expect(data.request.operator).not.undefined;
        done();
      })
      .catch(done);
  });

  it('should throw error when query file does not exist', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.graphql.OPERATOR_OK);
    }
    dataProvider
      .queryFile('/tmp/defined/path', {
        requestId: mockRequest.id,
      })
      .then(() => {
        done(new Error('Error is expected'));
      })
      .catch(err => {
        expect(err.message).to.contain('does not exist');
        done();
      });
  });
});
