'use strict';
const AWS = require('aws-sdk');
const path = require('path');
const sinon = require('sinon');
const expect = require('chai').expect;

const RequestBasedTemplateRenderer = require('../../helpers/renderer');
const fakeCalls = require('../fakeCalls');
const fixtures = require('../fixtures');

const UPDATE_FIXTURES = false;
const ONLINE_TEST = process.env.ONLINE_TEST === '1' || false;
const STAGE = process.env.STAGE || 'devel';
const REGION = process.env.REGION || 'eu-central-1';

describe('Request Template Renderer', () => {
  /**
   * @type {Lambda}
   */
  let lambda;

  /**
   * @type {RequestBasedTemplateRenderer}
   */
  let renderer;

  /**
   * @type {QRequest}
   */
  let mockRequest;

  beforeEach(() => {
    lambda = new AWS.Lambda({ region: REGION });
    mockRequest = fixtures.get('request/02.json');
    renderer = new RequestBasedTemplateRenderer(lambda, `templating-service-${STAGE}-render`);
  });

  it('should return the metadata and html rendered content with request information', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.templating.REQUEST_RENDERING_OK);
    } else {
      sinon.spy(lambda, 'invoke');
    }

    /**
     * @type {{request: QRequest}}
     */
    const context = fixtures.get('publishing/mock-context-invocation-response-payload.json').data;
    context.property = context.request.request.property;
    context.operator = context.request.operator;

    renderer
      .render(mockRequest, context)
      .then(result => {
        expect(result.content).to.contain(context.request.operator.fullName);

        lambda.invoke.getCalls()[0].returnValue.promise().then(res => {}).then(() => done());
      })
      .catch(done);
  });

  it('should invoke with accountName, propertyCode, language, id and context-data', done => {
    if (!ONLINE_TEST) {
      sinon.stub(lambda, 'invoke').callsFake(fakeCalls.lambda.templating.REQUEST_RENDERING_OK);
    } else {
      sinon.spy(lambda, 'invoke');
    }
    const context = fixtures.get('publishing/mock-context-invocation-response-payload.json').data;
    context.property = context.request.request.property;
    context.operator = context.request.operator;
    renderer
      .render(mockRequest, context)
      .then(() => {
        const invocationParameters = lambda.invoke.getCalls().pop().args[0];
        const event = JSON.parse(invocationParameters.Payload);
        expect(event.id).not.undefined;
        expect(event.accountName).not.undefined;
        expect(event.propertyCode).not.undefined;
        expect(event.language).not.undefined;
        expect(event.type).not.undefined;
        expect(event.data).not.undefined;
        expect(event.data.request).not.undefined;
        expect(event.data.request.operator).not.undefined;
        expect(event.data.request.operator.fullName).not.undefined;

        expect(event.id).to.be.equal('proposal');
        expect(event.type).to.be.equal('templates');
        expect(event.accountName).to.be.equal(mockRequest.request.accountName);
        expect(event.propertyCode).to.be.equal(mockRequest.request.propertyCode);
        expect(event.language).to.be.equal(mockRequest.language);
        done();
      })
      .catch(done);
  });
});
