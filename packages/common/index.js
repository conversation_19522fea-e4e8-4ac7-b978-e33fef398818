'use strict';
const request = require('./libs/request');
const account = require('./libs/account');
const operator = require('./libs/operator');
const mailer = require('./libs/mailer');
const tags = require('./libs/tags');
const dynamoUtils = require('./libs/dynamoUtilities');
const GraphQLDataProvider = require('./helpers/dataProvider');
const RequestBasedTemplateRenderer = require('./helpers/renderer');

module.exports = {
  helpers: {
    GraphQLDataProvider,
    RequestBasedTemplateRenderer,
  },
  request,
  account,
  operator,
  mailer,
  tags,
  dynamoUtils,
};
