{"name": "@quotelier/common", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "mocha ./tests/**/*.test.js -t 5000"}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "devDependencies": {"@types/chai": "^4.2.22", "aws-sdk": "^2.26.0", "chai": "^4.3.4", "fakeredis": "^2.0.0", "mocha": "^9.1.1", "redis": "^3.1.2", "redis-mock": "^0.56.3", "sinon": "^11.1.2"}, "dependencies": {"bluebird": "^3.5.0", "debug": "^4.3.2", "moment": "^2.17.1"}}