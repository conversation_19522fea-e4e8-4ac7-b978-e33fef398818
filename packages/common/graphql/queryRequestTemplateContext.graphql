query getRequestTemplateContext($requestId: String!) {
    request(id: $requestId) {
        id
        operatorId
        operator {
            id
            email
            fullName
            nickName
            title
            description
            phoneNumber
            photo {
                large,
                medium,
                small,
                title,
                xsmall
            }
            language
        }
        state
        createdAt
        template
        emailTemplate
        language
        contact {
            name
            email
            nickname
        }
        acceptedAt
        rejectedAt
        openedAt
        acceptedAt
        acceptedOfferId
        confirmedAt
        offers {
            rate {
                id
                board {
                    code,
                    type,
                    description,
                    photo
                }
                policies {
                    cancellation
                }
            }
            accommodationCodes
            serviceCodes
            services {
                id
                name
                description
                extra_price
                per_day
                per_room
                per_adult
                per_child
                per_infant
                required
                max_quantity
                fromd
                tod
                photos {
                    title
                    xsmall
                    small
                    medium
                    large
                }
                excl
            }
            accommodation {
                code
                name
                description
                amenities
                active
                capacity {
                    min_pers
                    max_pers
                    max_adults
                    children_allowed
                }
                photos {
                    title
                    xsmall
                    small
                    medium
                    large
                }
            }
            adults
            infants
            rooms
            children
            title
            description
            checkin
            nights
            officialRate
            roomRate
            discountRate
            taxesRate
            excludedCharges
            currency
            accepted
        }
        request {
            accountName
            propertyCode
            property {
                code
                name
                description
                bookurl
                url
                type
                currency
                logourl
                facilities
                rating
                contact {
                    tel
                    fax
                    email
                    skype
                }
                location {
                    lat
                    lon
                    utc_offset
                    timezone
                    name
                    address
                    zip
                    country
                }
                children {
                    allowed
                    age_from
                    age_to
                }
                settings {
                    nights_min
                    nights_max
                    rooms_max
                }
                operation {
                    checkout_time
                    checkin_time
                    open_from
                    open_to
                }
                photos {
                    title
                    xsmall
                    small
                    medium
                    large
                }
            }
            adults
            children
            infants
            checkin
            nights
            location
            channel
            tags
            rooms
            notes
            message
            releaseAt
        }
    }
}
