{"success": true, "response": [{"id": "customerCancelled", "theme": "proposal", "metadata": {"email": {"subject": "Your reservation at {{request.request.property.name}} has been cancelled", "from": "{{operator.email}}", "fromName": "{{operator.fullName}}"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n\n<p>\n  I would like to let you know that your reservation for {{formatDate request.request.checkin 'DD-MM-YYYY'}} and reference number {{request.id}} has been cancelled.\n</p>\n\n<p>\n  Please do not hesitate to contact me directly for any further information and we surely hope that we will welcome you as our guests some other time in the near future.\n</p>\n\n<p>\n  Best regards,\n</p>\n\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\nAdd a comment to this line\n</p>\n", "isDefault": false, "title": "Guest Confirmed Cancelled", "description": "The email sent to a guest when you cancel a confirmed proposal"}, {"id": "customerExpired", "theme": "proposal", "metadata": {"email": {"subject": "Your reservation at {{property.name}} expired"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n\n<p>\n  I would like to let you know that your reservation for {{formatDate request.request.checkin 'DD-MM-YYYY'}} and reference number {{request.id}} has been cancelled due to the fact that we never heard back from you in regards with the payment.\n</p>\n\n<p>\n  Please do not hesitate to contact me directly for any further information and we surely hope that we will welcome you as our guests some other time in the near future.\n</p>\n\n<p>\n  Best regards,\n</p>\n\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>\n", "isDefault": false, "title": "Guest Accepted Expired", "description": "The email sent to a guest when you cancel an accepted proposal"}, {"id": "customerExpiring", "theme": "proposal", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n<p>\n  How are you today?\n</p>\n<p>\n  I am just sending you a quick note to remind you that tomorrow our proposal expires.\n</p>\n<p>\n  If you want to proceed with your booking, now it is a good time to reserve your room.\n</p>\n<p>\n  {{requestLink}}\n</p>\n<p>\n  In case that you are still interested in booking your holidays at {{property.name}} and you need more time to consider your options, please do let me know so that I can check availability for you and see if I can extend the release date.\n</p>\n<p>\n  Look forward to receiving your reply.\n</p>\n<p>\n  Kindest regards,\n</p>\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>", "isDefault": false, "title": "Guest Expiring FollowUp", "description": "The follow up email sent to a guest 24h before the release date (if applicable)"}, {"id": "customerIdle", "theme": "proposal", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n<p>\n  I trust you are doing great!\n</p>\n<p>\n  Further to our last week’s correspondence, I was wondering if you had time to examine our proposal. \n</p>\n<p>\n  {{requestLink}}\n</p>\n<p>\n  Please keep in mind that the suggested accommodation is kept on option for you until {{request.request.releaseAt}}.\n</p>\n<p>\n  Looking forward to your reply and I remain at your disposal, should you wish us to check other information about your unparalleled holidays at {{property.name}}.\n</p>\n<p>\n  Kindest regards,\n</p>\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>", "isDefault": false, "title": "Guest Opened FollowUp", "description": "The follow up email sent when a guest has seen the proposal but hasn’t accepted or rejected the offer"}, {"id": "customerNew2", "theme": "proposal", "metadata": {"email": {"subject": "{{request.request.property.name}} | Reservation Proposal", "from": "{{operator.email}}", "fromName": "{{operator.fullName}}"}}, "language": "en", "template": "\n<p>\n  Dear asshole,\n</p>\n<p>\n  Further to your kind request, I have prepared a special quote for you and uploaded it on our secure server for your perusal.\n</p>\n<p>\n  To see this quote please click on the link below. (If the link is not active you can copy and paste it in your browser)\n</p>\n<p>\n  <a href=\"{{requestLink}}\">{{requestLink}}</a>\n</p>\n<p>\n  I remain at your disposal and do not hesitate to contact me directly if you have any questions.\n</p>\n<p>\n  Best regards,\n</p>\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>\n", "isDefault": false, "title": "customerNew2", "description": null}, {"id": "customerPaymentConfirmation", "theme": "proposal", "metadata": {"email": {"subject": "{{request.request.property.name}} | Payment Confirmation", "from": "{{operator.email}}", "fromName": "{{operator.fullName}}"}, "formats": {"date": "DD-MM-YYYY", "humanDate": "MMMM D YYYY", "period": ["DD MMMM", "DD MMMM YYYY"]}}, "language": "en", "template": "\n<p>\n    Dear {{request.contact.name}},\n</p>\n<p>\n    I'm happy to inform you that we received your payment regarding your stay\n    on {{request.request.checkin}}.\n</p>\n<p>\n    Your booking and all the services and amenities we have discussed are now\n    confirmed!\n</p>\n<p>\n    Please find below the accommodation summary for your records.\n</p>\n<h4>ACCOMMODATION SUMMARY</h4>\n<h5>Reference Number: {{ request.id }}</h5>\n{{#each request.offers as |offer offerKey|}}\n    {{#if offer.accepted}}\n        <table>\n            <tr>\n                <th>Accommodation:</th>\n                <th>{{offer.accommodation.[0].name}}</th>\n            </tr>\n            <tr>\n                <td>Checkin - Checkout:</td>\n                <td>{{ formatPeriod offer ../request.language\n                                    metadata.formats.period }}\n            </td>\n            </tr>\n            <tr>\n                <td>Nights:</td>\n                <td>{{ pluralize offer.nights 'Night' 'Nights' }}</td>\n            </tr>\n            <tr>\n                <td>Total pax:</td>\n                <td>\n                    {{\n                    separateWithComma\n                            (pluralize offer.adults 'Adult' 'Adults')\n                            (pluralize offer.children 'Child' 'Children')\n                            (pluralize offer.infants 'Infant' 'Infants')\n                    }}\n                </td>\n            </tr>\n            <tr>\n                <td>Rate:</td>\n                <td>{{ formatMoney offer.roomRate offer.currency }}</td>\n            </tr>\n            <tr>\n                <td>Accommodation Total:</td>\n                <td>{{ formatMoney (calcTotalPrice offer) offer.currency }}</td>\n            </tr>\n            {{#if offer.services.length}}\n                <tr>\n                    <td>Services:</td>\n                    <td>{{ join ',' offer.services 'name' }}</td>\n                </tr>\n            {{/if}}\n        </table>\n    {{/if}}\n{{/each}}\n<p>\n    Thank you once again for choosing {{request.request.propertyCode}} for your\n    carefree holidays. It was my pleasure communicating with you and of course,\n    I remain at your disposal for any additional information or assistance you\n    may require.\n</p>\n<p>\n    Kindest regards,\n</p>\n<p>\n    {{operator.fullName}}\n    <br>\n    {{operator.title}}\n    <br>\n    {{operator.email}}\n    <br>\n    {{operator.phoneNumber}}\n</p>\n", "isDefault": false, "title": "Guest Confirmation Email", "description": "The confirmation email when you confirm an accepted proposal"}, {"id": "customerPaymentOptions", "theme": "proposal", "metadata": {"email": {"subject": "{{request.request.property.name}} | Payment Options", "from": "{{request.operator.email}}", "fromName": "{{request.operator.fullName}}"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n<p>\n  Thank you for your prompt reply.\n</p>\n<p>\n  I am really glad to know that you wish to proceed with your booking and that we will have the pleasure to welcome you as\n  our guest.\n</p>\n<p>\n  We have now registered your booking so please take some time to read carefully our payment & cancellation policy:\n</p>\n<p>\n  {{offer.rate.policies.payment}}\n</p>\n{{#if request.request.releaseAt}}\n<p>\n  You room will be held on option until {{request.request.releaseAt}}.\n</p>\n{{/if}}\n<p>\n  The payment could be arranged by two methods.\n</p>\n<p>\n  <strong>Credit Card (Visa, MasterCard or AmEx)</strong>\n  <br> If you wish to proceed with the payment by card, we highly recommend you not to send your credit card details by email.\n  You could send them by fax at 0030 26950 41380 by downloading and filling out our <a href=\"https://quotelier.s3.amazonaws.com/securequote/hotels/lesante/CREDIT%20CARD%20AUTHORISATION.pdf\">credit card</a>  authorization form.\n</p>\n<p>\n  <strong>Bank Wire Transfer</strong>\n  <br> If you wish to proceed by bank wire transfer, please use our company’s [wire transfer](BANK-DETAILS.pdf) bank details.\n</p>\n<p>\n  In either case, once you proceed with the payment, please let me know which option and which method you chose, so that I\n  can send you a confirmation of reservation.\n</p>\n<p>\n  Awaiting for your response and looking forward to welcoming you as our guest!\n</p>\n<p>\n  Best regards,\n</p>\n<p>\n  {{request.request.propertyCode}}\n</p>\n", "isDefault": false, "title": "Guest Payment Options", "description": "The email with the payment options when a guest accepts an offer"}, {"id": "customerProposal", "theme": "proposal", "metadata": {}, "language": "en", "template": "<p>\n  Dear {{request.contact.name}},\n</p>\n<p>\n  Thank you for your interest in {{property.name}} for your guaranteed relaxation.\n</p>\n<p>\n  Below you will find our proposal and useful information about your stay.\n</p>\n<p>\n  In case that you have queries, feel free to contact your holiday consultant directly.\n</p>\n<p>\n  Kind regards,\n</p>\n<p>\n  {{operator.nickName}}\n</p>", "isDefault": false, "title": "customerProposal", "description": null}, {"id": "customerUnpaid", "theme": "proposal", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\nDear {{request.contact.name}},\n\nFurther to the registration of your booking a few days ago, please note that we have not yet received an email from yourself with regards to the payment.\n\nOnce you proceed with the payment, please do let me know which option and which method you chose, so that I can send you a confirmation of your reservation.\n\nI look forward to receiving your update and I remain at your disposal.\n\nKindest regards,\n\n{{operator.name}}", "isDefault": false, "title": "Guest Accepted FollowU<PERSON>", "description": "The follow up email sent when a guest has accepted an offer but hasn’t paid."}, {"id": "customerUnread", "theme": "proposal", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\nDear {{request.contact.name}},\n\nI hope that my email finds you really well!\n\nFurther to our previous communication, I am writing in order to find out if you had the chance to check our proposal and whether it was to your liking. \n\n{{requestLink}}\n\nShould you wish us to check other information about your relaxing holidays at {{property.name}}, feel free to email me directly and I will be happy to be of assistance.\n\nKindest regards,\n\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>", "isDefault": false, "title": "Guest <PERSON><PERSON>", "description": "The follow up email sent when a proposal hasn’t been opened"}, {"id": "operatorAccepted", "theme": "proposal", "metadata": {"email": {"subject": "REQUEST ACCEPTED | {{request.contact.name}}"}}, "language": "en", "template": "\n\n<mjml>\n   <mj-head>\n     <mj-font name=\"Raleway\" href=\"https://fonts.googleapis.com/css?family=Raleway\" />\n   </mj-head>\n  <mj-body>\n    <mj-container>\n      <mj-section>\n        <mj-column background-color=\"#ffffff\">\n          <mj-text font-size=\"16px\" align=\"center\" color=\"#ffffff\" container-background-color=\"#457fff\" font-family=\"Raleway, Arial\">REQUEST ACCEPTED | {{request.contact.name}}\n          </mj-text>\n          <mj-text font-size=\"14px\">Hurray! {{request.contact.name}}, has accepted your proposal.\n            <br>\n            <br>View and complete your tasks by visiting your Quotelier Account.\n          </mj-text>\n          <mj-button background-color=\"#4b80ff\" font-family=\"Arial, Helvetica, sans-serif\" href=\"{{ACCOUNT}}\">Open in Quotelier\n            <br>\n          </mj-button>\n          <mj-divider border-width=\"2\" border-color=\"#bebebe\">\n          </mj-divider>\n          <mj-text font-size=\"11px\" align=\"center\" color=\"#6f6f6f\">If you'd rather not receive new requests reminders, you can turn it off [here]({SETTINGS}).\n            <br>If you don't want to receive reminders about accepted requests, you can turn them off [here]({SETTINGS})\n          </mj-text>\n        </mj-column>\n      </mj-section>\n    </mj-container>\n  </mj-body>\n</mjml>\n", "isDefault": false, "title": "Operator Accepted", "description": "The email sent when a guest has accepted an offer."}, {"id": "operatorFollowup", "theme": "proposal", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\n{{operator.name}},\n\nWe know you're busy but this is a kind reminder that {TOTAL} follow ups are pending.\n\nView and complete your tasks by visiting your Quotelier Account [here]({ACCOUNT}).\n\nIf you don't want to receive reminders about pending requests, you can turn them off [here]({SETTINGS}).", "isDefault": false, "title": "Operator FollowUp", "description": "The email sent when a request is marked to followUp."}, {"id": "operatorNew", "theme": "proposal", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\n{{operator.name}},\n\nGreat news! You have received a new request for {{request.request.checkin}}.\n\nView and complete your tasks by visiting your Quotelier Account [here]({ACCOUNT}).\n\nIf you don't want to receive reminders about new requests, you can turn them off [here]({SETTINGS}).", "isDefault": false, "title": "Operator New", "description": "The email sent when a new request is submitted."}, {"id": "operatorRejected", "theme": "proposal", "metadata": {"email": {"subject": "REQUEST REJECTED | {{request.contact.name}}"}}, "language": "en", "template": "\n{{request.operator.fullName}},\n\n{{request.offers.length}} proposals were rejected on {yesterday}.\n\nFind out why by visiting your Quotelier Account [here]({ACCOUNT}).\n\nIf you don't want to receive reminders about rejected requests, you can turn them off [here]({SETTINGS}).", "isDefault": false, "title": "Operator Rejected", "description": "The email sent when the guest has rejected the proposal."}, {"id": "customerNew", "theme": "none", "metadata": {"email": {"subject": "{{request.request.property.name}} | Reservation Proposal", "from": "{{operator.email}}", "fromName": "{{operator.fullName}}"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n<p>\n  Further to your kind request, I have prepared a special quote for you and uploaded it on our secure server for your perusal.\n</p>\n<p>\n  To see this quote please click on the link below. (If the link is not active you can copy and paste it in your browser)\n</p>\n<p>\n  <a href=\"{{requestLink}}\">{{requestLink}}</a>\n</p>\n<p>\n  I remain at your disposal and do not hesitate to contact me directly if you have any questions.\n</p>\n<p>\n  Best regards,\n</p>\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>\n", "isDefault": true, "title": "Guest <PERSON>er", "description": "The first email sent to a guest with the link for the proposal"}, {"id": "customerUnavailableCancelled", "theme": "none", "metadata": {"email": {"subject": "Your reservation at {{request.request.property.name}} has been cancelled", "from": "{{operator.email}}", "fromName": "{{operator.fullName}}"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n\n<p>\n  I would like to let you know that your reservation for {{formatDate request.request.checkin 'DD-MM-YYYY'}} and reference number {{request.id}} has been cancelled.\n</p>\n\n<p>\n  Please do not hesitate to contact me directly for any further information and we surely hope that we will welcome you as our guests some other time in the near future.\n</p>\n\n<p>\n  Best regards,\n</p>\n\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\nAdd a comment to this line\n</p>\n", "isDefault": true, "title": "Guest Cancelled Accepted Unavailable", "description": "The email sent when a guest has accepted an offer without availability"}, {"id": "customerWaitlisted", "theme": "none", "metadata": {"email": {"subject": "{{request.request.property.name}} | Waitlisted", "from": "{{operator.email}}", "fromName": "{{operator.fullName}}"}}, "language": "en", "template": "\n<p>\n  Dear {{request.contact.name}},\n</p>\n<p>\n  Your request has been waitlisted.\n</p>\n<p>\n  I remain at your disposal and do not hesitate to contact me directly if you have any questions.\n</p>\n<p>\n  Best regards,\n</p>\n<p>\n  {{operator.fullName}}\n  <br>\n  {{operator.title}}\n  <br>\n  {{operator.email}}\n  <br>\n  {{operator.phoneNumber}}\n</p>\n", "isDefault": true, "title": "Guest Waitlisted", "description": "The email sent when a reservation-request has been added to the wait-list."}, {"id": "operatorExpiring", "theme": "none", "metadata": {"email": {"subject": "Not Implemented"}}, "language": "en", "template": "\n{{operator.name}},\n\nRequest with reference No {{request.id}}, is about to expire.\n\nView and complete your tasks by visiting your Quotelier Account [here]({ACCOUNT}).\n", "isDefault": true, "title": "Operator Expiring", "description": "The email sent when a request is about to expire."}, {"id": "operatorUnavailable", "theme": "none", "metadata": {"email": {"subject": "REQUEST UNAVAILABLE | {{request.contact.name}}"}}, "language": "en", "template": "\n{{operator.fullName}},\n\nRequest with reference No {{request.id}}, is in unavailable state.\n\nView and complete your tasks by visiting your Quotelier Account [here]({ACCOUNT}).\n", "isDefault": true, "title": "Operator Unavailable", "description": "The email sent when the accepted offer was unavailable."}]}