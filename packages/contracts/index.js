const fs = require('fs');
const path = require('path');

/**
 * @param {String} filePath
 * @returns {Object}
 */
function getContract(filePath) {
  return JSON.parse(fs.readFileSync(path.join(__dirname, `${filePath}.json`), 'utf-8'));
}

/**
 * @param {String} type
 * @param {String} message
 * @returns {Object}
 */
function getErrorContract(type, message) {
  return {
    success: false,
    response: null,
    error: {
      type: type || 'ValidationError',
      message: message || 'Field is required',
    },
  };
}

module.exports = {
  getContract,
  getErrorContract,
};
