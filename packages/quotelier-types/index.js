const jsonschema = require('jsonschema');

/**
 * @param {any} validator
 * @returns {any}
 */
function registerSchemas(validator) {
  const definitions = require('./schemas/definitions.json');

  Object.keys(definitions).forEach(definitionName => {
    const schema = definitions[definitionName];
    schema['id'] = `/${definitionName}`;
    validator.addSchema(schema, schema['id']);
  });

  return validator;
}

/**
 * @throws {ValidationError}
 *
 * @param {object} input
 * @param {object} schema
 * @returns {object}
 */
function validate(input, schema) {
  const validator = new jsonschema.Validator();
  registerSchemas(validator);
  const res = validator.validate(input, schema);

  if (res.errors.length) {
    const err = new Error(formatErrors(res.errors));
    err.name = 'ValidationError';
    throw err;
  } else {
    return input;
  }
}
/**
 * @param {any} errors
 * @returns {string}
 */
function formatErrors(errors) {
  return errors
    .map(error => {
      return `${error.property}: ${error.message}`;
    })
    .join(`, `);
}

module.exports = { registerSchemas, validate };
