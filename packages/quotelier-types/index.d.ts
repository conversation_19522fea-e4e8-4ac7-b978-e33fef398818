declare interface QAccount {
  accountName: string;
  propertyCode: string;
  type: string;
}

declare interface QOperatorDetails extends QContactable {
  id: string;
  fullName: string;
  nickName: string;
  title: string;
  description: string;
  phoneNumber: string;
  photo: QPhoto;
  language?: string;
}

declare interface QContactable {
  /**
   * @format email
   */
  email: string;
  name?: string | null;
  cc?: string | null;
}

declare interface QCustomerContactInfo extends QContactable {
  name?: string | null;
  title?: string | null;
  firstName: string;
  lastName: string;
  nickname: string;
  country?: string | null;
  wiped?: boolean;

  /**
   * Only available on handlebars rendering context,
   * we dont persist this value because it is dynamically signed
   */
  landingPageUrl?: string;
}

declare interface QContact extends QContactable {
  tel: string
  fax: string
  skype: string
}

declare interface QEmailAttachment {
  /**
   * The content MimeType
   */
  type: string;
  /**
   * The filename
   */
  name: string;
  /**
   * Base64 encoded content
   */
  content: string;
}

declare interface QLocation {
  lat?: number | null
  lon?: number | null
  utc_offset: number
  timezone: string
  name: string
  address: string
  zip: string
  country: string
}

declare interface QChildren {
  allowed: number
  age_from: number
  age_to: number
}

declare interface QSettings {
  nights_min: number
  nights_max: number
  rooms_max: number
}

declare interface QProperty {
  code: string
  name: string
  description: string
  bookurl: string
  url: string
  type: string
  currency: string
  logourl: string
  facilities: string[]
  rating: number
  contact: QContact
  location: QLocation
  children: QChildren
  settings: QSettings
  automode: QAutoMode
  operation: any // QOperatorDetails
  photos: QPhoto[]
}

declare interface QRequestDetails extends QPrimitiveRequestDetails {
  property: QProperty;
}

declare interface QPrimitiveRequestDetails {
  accountName: string;
  propertyCode: string;
  adults: number;
  children: number;
  infants: number;
  nights: number;
  checkin: string;
  rooms: number;
  location: string;
  channel: string;
  tags: string[];
  notes: string;
  message: string;
  expireAt: string;
}

declare interface QUpsaleDetails extends QPrimitiveUpsaleDetails {
  property: QProperty;
}

declare interface QPrimitiveUpsaleDetails {
  accountName: string;
  propertyCode: string;
  tags: string[];
  notes: string;
  message: string;
}

declare interface QConstraints {
  expiration: string;
  earlyBookLimit: any;
  freeCancelDays: any;
}

declare interface QPolicies {
  cancellation: string;
  cancellation_policy_id: number | string
  payment: string;
  payment_policy_id: number | string
}

declare interface QPolicy {
  id: string | number
  name: string
  description: string
}

declare interface QCancellationPolicy extends QPolicy { }

declare interface QPaymentPolicy extends QPolicy { }

declare interface QBoard {
  code: string | number;
  type: string;
  description?: string;
  photo?: string;
}

declare interface QRate {
  id: string | number;
  room: string;
  name: string;
  boardCode: number;
  board: QBoard;
  active: number;
  public: number;
  parent: number;
  virtual: number;
  currency: string;
  roomName: string;
  parentRate: number;
  fromd: string;
  tod: string;
  constraints: QConstraints;
  description: string;
  presentation: string;
  policies: QPolicies;
}
declare interface QCapacity {
  min_pers: number;
  max_pers: number;
  max_aduts?: number;
  children_allowed: boolean;
}

declare interface QPhoto {
  title?: string;
  xsmall?: string;
  small: string;
  medium?: string;
  large?: string;
}

declare interface QRoom {
  code: string;
  name: string;
  description: string;
  amenities: string[];
  active: boolean;
  capacity: QCapacity;
  photos: QPhoto[];
}

declare interface QService {
  id: string | number;
  name: string;
  description: string;
  extra_price?: number;
  per_day: number;
  per_adult: number;
  per_child: number;
  per_infant: number;
  per_room: number;
  required?: boolean;
  max_quantity: number;
  fromd: string;
  tod: string;
  photos: QPhoto[];
  excl?: string[];
}

declare interface QPrimitiveOffer {
  rateId: string;
  propertyCode: string;
  /**
   * @minItems 1
   * @maxItems 5
   */
  accommodationCodes: string[];
  serviceCodes: string[];
  title: string;
  description: string;
  /**
   * @format date
   */
  checkin: string;
  nights: number;
  rooms: number;
  children: number;
  /**
   * @minimum 1
   */
  adults: number;
  infants: number;
  officialRate: number;
  roomRate: number;
  discountRate?: number;
  serviceTotalRate?: number;
  reservationId?: string;
  taxesRate?: number;
  excludedCharges?: number;
  currency: string;
  accepted?: boolean;
  paymentUrl?: string;
}

declare interface QOffer extends QPrimitiveOffer {
  accommodation: QRoom[];
  rate: QRate;
  services: QService[];
  reservation?: QReservation;
}

declare interface QRequestActivities {
  created: string;
  updated: string;
  new: string;
  draft: string;
  pending: string;
  sent: string;
  accepted: string;
  rejected: string;
  opened: string;
  expired: string;
  confirmed: string;
  archived: string;
  markedToFollowUp: string;
  failed: string;
}

declare interface QRequest extends QPrimitiveRequest {
  operator: QOperatorDetails;
  request: QRequestDetails;
  offers: QOffer[];
}

declare interface QPrimitiveRequest {
  id: string;
  operatorId: string;
  proposalUri: string;
  state: string;
  /**
   * @deprecated
   */
  createdAt: string;
  /**
   * @deprecated
   */
  updatedAt: string;
  /**
   * @deprecated
   */
  markedToFollowUp: string | null;
  template: string;
  emailTemplate: string;
  muted: boolean;
  language: string;
  contact: QCustomerContactInfo;
  request: QPrimitiveRequestDetails;
  offers: QPrimitiveOffer[];
  /**
 * @deprecated
 */
  acceptedAt: string;
  /**
   * @deprecated
   */
  rejectedAt: string;
  /**
   * @deprecated
   */
  acceptedOfferId: string;
  acceptedOptionIds: string[];
  activities: QRequestActivities
  errors: string[];
  options: QRequestOptions;
  attributes?: {
    [k: string]: string | number;
  }
}

declare interface QRequestOptions {
  flowType: string
}

declare interface QAutoMode {
  enabled: boolean;
  prefix: string;
  template: string;
  operator: string;
}

declare interface QPropertySettings {
  locale: QLocaleSettings;
  hours: QHoursSettings;
  schedule: QScheduleSettings;
}

declare interface QLocaleSettings {
  utcOffset: string;
}

declare interface QScheduleSettings {
  expireAfterSend: number;
}

declare interface QHoursSettings {
  releaseHour: number;
  expireHour: number;
}

declare interface QReservation {
  reservationId: string;
  status: 'active' | 'canceled';
  propertyCode: string
  accommodationCode: string
  accommodationName: string
  rateId: string
  externalId?: string
  boardCode?: string
  checkin: string
  checkout: string
  rooms: number
  nights: number
  adults: number
  children: number
  infants: number
  attributes: { [k: string]: string | number | boolean }
  roomRate: number
  currency: string
  serviceCodes: string[]
  contact: QCustomerContactInfo
}

declare interface QUpgradeOpportunity {
  reservation: QReservation;
  offers: QPrimitiveOffer[]
}

declare interface QOpportunity {
  request: QRequestDetails
  reservation: QReservation
  opportunity: QOffer
}

declare interface QUpsaleActivities {
  created: string;
  updated: string;
  draft: string;
  pending: string;
  sent: string;
  accepted: string;
  opened: string;
  confirmed: string;
  archived: string;
}

declare interface QPrimitiveUpsale {
  id: string;
  operatorId: string;
  proposalUri: string;
  state: string;
  /**
   * @deprecated
   */
  createdAt: string;
  /**
   * @deprecated
   */
  updatedAt: string;
  template: string;
  emailTemplate: string;
  muted: boolean;
  language: string;
  contact: QCustomerContactInfo;
  request: QPrimitiveUpsaleDetails;
  reservationId: string;
  reservation: QReservation;
  activities: QUpsaleActivities;
  offers: QPrimitiveOffer[];
  /**
   * @deprecated
   */
  acceptedAt: string;
  /**
   * @deprecated
   */
  openedAt: string;
  acceptedOfferId: string;
  /**
   * @deprecated
   */
  confirmedAt: string;
}

declare interface QUpgradeActivities {
  created: string;
  updated: string;
  pending?: string;
  sent?: string;
  accepted?: string;
  opened?: string;
  draft?: string;
  archived?: string;
}

declare interface QPrimitiveUpgradeDetails {
  accountName: string;
  propertyCode: string;
  operatorId: string;
  template?: string;
  language?: string;
  muted?: boolean;
  message?: string | null;
  tags?: string[];
}

declare interface QStateError {
  Error: string;
  Cause: string;
}

declare interface QPrimitiveUpgrade {
  id: string;
  state: string;
  activities: QUpgradeActivities;
  contact: QCustomerContactInfo;
  offers: QPrimitiveOffer[];
  reservation: QReservation;
  details: QPrimitiveUpgradeDetails;
  error?: string | null;
}
declare interface QUpgradeDetails extends QPrimitiveUpgradeDetails {
  operator: QOperatorDetails;
  property: QProperty;
}

declare interface QUpgrade extends QPrimitiveUpgrade {
  offers: QOffer[];
  details: QUpgradeDetails;
  reservation: QReservation;
}

declare interface QUpsale extends QPrimitiveUpsale {
  operator: QOperatorDetails;
  reservation: QReservation;
  request: QUpsaleDetails;
  offers: QOffer[];
}

declare interface QAvailabilityOffer {
  rateId: string;
  boardId: number;
  rate: string;
  accommodationCode: string;
  serviceCode: string;
  room: string;
  currency: string;
  paymentPolicyId: number | string;
  cancellationPolicyId: number | string;
  remaining: number;
  roomRate: number;
  officialRate: number;
  discountRate: number;
  taxesRate: number;
  excludedCharges?: number;
}

declare interface QTemplateItem {
  id: string;
  type: string;
  language: string;
  theme?: string;
  basename?: string;
  /**
   * The template content
   */
  template: string;
  /**
   * Metadata, used in template context.
   */
  metadata: { [k: string]: any };
}

/**
 * The processed template-item result: rendered template data
 */
declare interface QTemplateData {
  theme: string;
  content: string;
  metadata: { [k: string]: any };
}