const { join } = require('path');
const fs = require('fs');
const TJS = require('typescript-json-schema');

// optionally pass argument to schema generator
const settings = {
  required: true,
};

const program = TJS.programFromConfig(join(__dirname, 'tsconfig.json'));

const generator = TJS.buildGenerator(program, settings);
const symbols = generator.getUserSymbols();

if (!fs.existsSync(join(__dirname, 'schemas'))) {
  fs.mkdirSync((__dirname, 'schemas'));
}

const definitions = {};
symbols.forEach(symbol => {
  const res = generator.getSchemaForSymbol(symbol);
  definitions[symbol] = res;
});

fs.writeFileSync(
  join(__dirname, 'schemas', `definitions.json`),
  JSON.stringify(definitions, null, 4)
);
