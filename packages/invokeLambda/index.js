const logger = require('@quotelier/logger');

const AWS = require('aws-sdk');

const lambda = new AWS.Lambda({ region: process.env.REGION });

/**
 * @param {string} lambdaName
 * @param {Object} payload
 * @param {Object} context
 * @param {Lambda} [customLambda]
 * @returns {Promise<Object>}
 */
function invokeLambda(lambdaName, payload, context, customLambda) {
  logger.log('invokeLambda', `Trying to invoke ${lambdaName}`, payload);
  return (customLambda || lambda)
    .invoke({
      FunctionName: lambdaName,
      Payload: JSON.stringify(payload),
      ClientContext: AWS.util.base64.encode(JSON.stringify(context)),
      // @see https://github.com/aws/aws-sdk-js/issues/1388
    })
    .promise()
    .then(response => handleResponse(response, lambdaName));
}

/**
 * @param {string} serviceName
 * @param {string} lambdaName
 * @param {Object} payload
 * @param {Object} context
 * @param {Lambda} [customLambda]
 * @returns {Promise<Object>}
 */
function invokeService(serviceName, lambdaName, payload, context, customLambda) {
  logger.log('invokeService', `Trying to invoke ${serviceName}/${lambdaName}`, payload);

  if (!process.env.STAGE) {
    throw new Error('STAGE env variable, is not set.');
  }

  const stage = process.env.STAGE;

  return invokeLambda(`${serviceName}-${stage}-${lambdaName}`, payload, context, customLambda);
}

/**
 * @param {string} lambdaName
 * @param {Object} payload
 * @param {Object} context
 * @param {Lambda} [customLambda]
 * @returns {Promise<Object>}
 */
function invokeLambdaAsync(lambdaName, payload, context, customLambda) {
  logger.log('invokeLambda', `Trying to invoke ${lambdaName}`, payload);
  return (customLambda || lambda)
    .invoke({
      FunctionName: lambdaName,
      InvocationType: 'Event',
      Payload: JSON.stringify(payload),
      ClientContext: AWS.util.base64.encode(JSON.stringify(context)),
      // @see https://github.com/aws/aws-sdk-js/issues/1388
    })
    .promise()
    .then(response => handleResponseAsync(response, lambdaName));
}

/**
 * @param {string} serviceName
 * @param {string} lambdaName
 * @param {Object} payload
 * @param {Object} context
 * @param {Lambda} [customLambda]
 * @returns {Promise<Object>}
 */
function invokeServiceAsync(serviceName, lambdaName, payload, context, customLambda) {
  logger.log('invokeServiceAsync', `Trying to invoke ${invokeService}/${lambdaName}`, payload);

  if (!process.env.STAGE) {
    throw new Error('STAGE env variable, is not set.');
  }

  const stage = process.env.STAGE;

  return invokeLambdaAsync(`${serviceName}-${stage}-${lambdaName}`, payload, context, customLambda);
}

/**
 * @private
 * @param {Object} response
 * @param {string} lambdaName
 * @returns {Object}
 * @throws {Error}
 */
function handleResponse(response, lambdaName) {
  logger.log('handleResponse', `Invocation response ${lambdaName}`, response);

  if (response.StatusCode === 200) {
    let payload = JSON.parse(response.Payload);
    if (payload && payload['errorMessage']) {
      // Invocation internal error
      let error = new Error(payload['errorMessage']);
      if (payload['errorType']) {
        error.type = payload['errorType'];
      }
      throw error;
    } else {
      return payload;
    }
  } else {
    throw new Error(`couldn't reach lambda function: ${lambdaName}`);
  }
}

/**
 * @private
 * @param {Object} response
 * @param {string} lambdaName
 * @returns {Object}
 * @throws {Error}
 */
function handleResponseAsync(response, lambdaName) {
  logger.log('handleResponseAsync', `Invocation response ${lambdaName}`, response);

  if (response.StatusCode !== 202) {
    throw new Error(`couldn't reach lambda function: ${lambdaName}`);
  }

  return true;
}

module.exports = {
  invokeLambda,
  invokeLambdaAsync,
  invokeService,
  invokeServiceAsync,
};
