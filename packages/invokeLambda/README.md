# invokeLambda

## Description
This is our shared package to request data from other services. Currently it
serves as a wrapper on top of `aws-sdk` and invokes other AWS Lambdas.

## API
```
const {invokeLambda, invokeLambdaAsync} = require('invokeLambda');

// On sync vs async see: http://docs.aws.amazon.com/lambda/latest/dg/retries-on-errors.html
// and http://docs.aws.amazon.com/lambda/latest/dg/API_Invoke.html#API_Invoke_RequestSyntax

invokeLambda(LAMBDA_FUNCTION_NAME, payload)
  .then(response => // This is the response from the Lambda either it failed or not)
  .catch(err => // Probably a network error)

invokeLambdaAsyn(LAMBDA_FUNCTION_NAME, payload)
  .then(response => // This is the response from the Lambda either it failed or not)
  .catch(err => // Probably a network error)

```

## TODO
* Add tests
* Add caching mechanism to avoid duplicate requests on the same process
